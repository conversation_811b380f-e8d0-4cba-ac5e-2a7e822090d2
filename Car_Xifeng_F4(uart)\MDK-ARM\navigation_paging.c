/**
 * 分页导航显示系统实现
 */

#include "navigation_paging.h"
#include "navigation_routes.h"
#include "touch_driver.h"

// 全局变量
NavigationPaging_t g_nav_paging = {0};
static NavigationStep_t g_nav_steps[20] = {0}; // 最多20个导航步骤
static NavigationPageType_t g_current_page_type = NAV_PAGE_STEPS;

/**
 * @brief 初始化分页导航系统
 */
void NavPaging_Init(void)
{
    // 初始化触摸屏
    Touch_Init();
    
    // 初始化分页状态
    g_nav_paging.steps = g_nav_steps;
    g_nav_paging.total_steps = 0;
    g_nav_paging.current_page = 0;
    g_nav_paging.steps_per_page = 4; // 每页显示4个步骤
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_step = 0;
    g_nav_paging.total_distance = 3.78f;
    g_nav_paging.estimated_time = 4;
    
    // 加载万达路线
    NavRoutes_LoadWandaRoute();
}

// NavPaging_LoadRoute函数已移动到navigation_routes.c中

/**
 * @brief 添加导航步骤
 */
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance, 
                       const char* road_name, uint8_t direction)
{
    if (g_nav_paging.total_steps < 20) {
        NavigationStep_t* step = &g_nav_steps[g_nav_paging.total_steps];
        step->step_num = step_num;
        strncpy(step->instruction, instruction, sizeof(step->instruction) - 1);
        strncpy(step->distance, distance, sizeof(step->distance) - 1);
        strncpy(step->road_name, road_name, sizeof(step->road_name) - 1);
        step->direction = direction;
        step->completed = 0;
        
        g_nav_paging.total_steps++;
    }
}

/**
 * @brief 下一页
 */
void NavPaging_NextPage(void)
{
    if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
        g_nav_paging.current_page++;
    }
}

/**
 * @brief 上一页
 */
void NavPaging_PrevPage(void)
{
    if (g_nav_paging.current_page > 0) {
        g_nav_paging.current_page--;
    }
}

/**
 * @brief 设置当前步骤
 */
void NavPaging_SetCurrentStep(uint8_t step_num)
{
    g_nav_paging.current_step = step_num;
    
    // 自动跳转到包含当前步骤的页面
    if (step_num > 0) {
        uint8_t target_page = (step_num - 1) / g_nav_paging.steps_per_page;
        g_nav_paging.current_page = target_page;
    }
}

/**
 * @brief 完成步骤
 */
void NavPaging_CompleteStep(uint8_t step_num)
{
    if (step_num <= g_nav_paging.total_steps) {
        g_nav_steps[step_num - 1].completed = 1;
    }
}

/**
 * @brief 获取每页显示的步骤数
 */
uint8_t NavPaging_GetStepsPerPage(void)
{
    return g_nav_paging.steps_per_page;
}

/**
 * @brief 处理触摸事件
 */
void NavPaging_HandleTouch(TouchEvent_t event, uint16_t x, uint16_t y)
{
    uint8_t area = Touch_GetArea(x, y);
    
    switch (event) {
        case TOUCH_EVENT_PRESS:
            switch (area) {
                case TOUCH_AREA_PREV_PAGE:
                    NavPaging_PrevPage();
                    break;
                case TOUCH_AREA_NEXT_PAGE:
                    NavPaging_NextPage();
                    break;
                case TOUCH_AREA_BACK:
                    // 返回主界面
                    break;
                case TOUCH_AREA_HOME:
                    // 回到首页
                    g_nav_paging.current_page = 0;
                    break;
            }
            break;
            
        case TOUCH_EVENT_SWIPE_LEFT:
            NavPaging_NextPage();
            break;
            
        case TOUCH_EVENT_SWIPE_RIGHT:
            NavPaging_PrevPage();
            break;
            
        case TOUCH_EVENT_SWIPE_UP:
            // 向上滑动 - 可以用于其他功能
            break;
            
        case TOUCH_EVENT_SWIPE_DOWN:
            // 向下滑动 - 可以用于其他功能
            break;
            
        default:
            break;
    }
}

/**
 * @brief 主显示函数
 */
void NavPaging_Display(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);

    // 绘制头部
    NavPaging_DrawHeader();

    // 绘制导航步骤
    NavPaging_DrawSteps();

    // 绘制页面指示器
    NavPaging_DrawPageIndicator();

    // 绘制触摸按钮
    NavPaging_DrawTouchButtons();
}

/**
 * @brief 绘制头部信息
 */
void NavPaging_DrawHeader(void)
{
    char buffer[64];

    // 标题
    LCD_ShowString(10, 5, (const uint8_t*)"WANDA Navigation", WHITE, BLACK, 16, 0);

    // 总距离
    snprintf(buffer, sizeof(buffer), "%.2fkm", g_nav_paging.total_distance);
    LCD_ShowString(10, 25, (const uint8_t*)buffer, CYAN, BLACK, 14, 0);

    // 预计时间
    snprintf(buffer, sizeof(buffer), "%dmin", g_nav_paging.estimated_time);
    LCD_ShowString(80, 25, (const uint8_t*)buffer, CYAN, BLACK, 14, 0);

    // 路径规划
    LCD_ShowString(140, 25, (const uint8_t*)"OSRM", GREEN, BLACK, 12, 0);

    // 当前步骤信息
    snprintf(buffer, sizeof(buffer), "Step %d/%d", g_nav_paging.current_step, g_nav_paging.total_steps);
    LCD_ShowString(180, 25, (const uint8_t*)buffer, YELLOW, BLACK, 12, 0);

    // 分隔线
    LCD_DrawLine(5, 45, LCD_W - 5, 45, WHITE);
}

/**
 * @brief 绘制导航步骤
 */
void NavPaging_DrawSteps(void)
{
    uint16_t y_start = 55;
    uint16_t step_height = 35;
    uint8_t start_step = g_nav_paging.current_page * g_nav_paging.steps_per_page;
    uint8_t end_step = start_step + g_nav_paging.steps_per_page;

    if (end_step > g_nav_paging.total_steps) {
        end_step = g_nav_paging.total_steps;
    }

    for (uint8_t i = start_step; i < end_step; i++) {
        NavigationStep_t* step = &g_nav_steps[i];
        uint16_t y_pos = y_start + (i - start_step) * step_height;

        // 确定步骤颜色
        uint16_t step_color = GREEN;
        if (step->completed) {
            step_color = GRAY;
        } else if (step->step_num == g_nav_paging.current_step) {
            step_color = YELLOW;
        }

        // 绘制步骤
        NavPaging_DrawSingleStep(step, y_pos, step_color);
    }
}

/**
 * @brief 绘制单个导航步骤
 */
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color)
{
    char buffer[8];

    // 绘制步骤编号圆圈
    Draw_Circle(20, y_pos + 12, 10, color);
    if (step->step_num < 10) {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(17, y_pos + 8, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);
    } else {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(14, y_pos + 8, (const uint8_t*)buffer, WHITE, BLACK, 10, 0);
    }

    // 绘制连接线（除了页面最后一步）
    uint8_t is_last_on_page = ((step->step_num - 1) % g_nav_paging.steps_per_page) == (g_nav_paging.steps_per_page - 1);
    uint8_t is_last_step = (step->step_num == g_nav_paging.total_steps);

    if (!is_last_on_page && !is_last_step) {
        LCD_DrawLine(20, y_pos + 22, 20, y_pos + 40, CYAN);
        LCD_DrawLine(19, y_pos + 22, 19, y_pos + 40, CYAN);
        LCD_DrawLine(21, y_pos + 22, 21, y_pos + 40, CYAN);
    }

    // 绘制导航指令（分两行显示）
    LCD_ShowString(40, y_pos + 2, (const uint8_t*)step->instruction, WHITE, BLACK, 10, 0);
    LCD_ShowString(40, y_pos + 15, (const uint8_t*)step->road_name, GRAY, BLACK, 10, 0);
    LCD_ShowString(40, y_pos + 25, (const uint8_t*)step->distance, CYAN, BLACK, 10, 0);

    // 绘制方向箭头
    NavPaging_DrawDirectionArrow(step->direction, 200, y_pos + 12);
}

/**
 * @brief 绘制方向箭头
 */
void NavPaging_DrawDirectionArrow(uint8_t direction, uint16_t x, uint16_t y)
{
    switch (direction) {
        case 0: // 直行
            LCD_DrawLine(x+6, y-6, x+2, y-2, YELLOW);
            LCD_DrawLine(x+6, y-6, x+10, y-2, YELLOW);
            LCD_DrawLine(x+6, y-6, x+6, y+6, YELLOW);
            break;

        case 1: // 左转
            LCD_DrawLine(x, y, x+8, y-4, YELLOW);
            LCD_DrawLine(x, y, x+8, y+4, YELLOW);
            LCD_DrawLine(x, y, x+12, y, YELLOW);
            break;

        case 2: // 右转
            LCD_DrawLine(x+12, y, x+4, y-4, YELLOW);
            LCD_DrawLine(x+12, y, x+4, y+4, YELLOW);
            LCD_DrawLine(x, y, x+12, y, YELLOW);
            break;

        case 3: // 到达
            Draw_Circle(x+6, y, 4, RED);
            Draw_Circle(x+6, y, 3, RED);
            break;
    }
}

/**
 * @brief 绘制页面指示器
 */
void NavPaging_DrawPageIndicator(void)
{
    char buffer[32];
    uint16_t y_pos = LCD_H - 60;

    // 页面信息
    snprintf(buffer, sizeof(buffer), "Page %d/%d", g_nav_paging.current_page + 1, g_nav_paging.total_pages);
    LCD_ShowString(LCD_W/2 - 30, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);

    // 页面点指示器
    uint16_t dot_x = LCD_W/2 - g_nav_paging.total_pages * 6;
    for (uint8_t i = 0; i < g_nav_paging.total_pages; i++) {
        uint16_t color = (i == g_nav_paging.current_page) ? WHITE : GRAY;
        Draw_Circle(dot_x + i * 12, y_pos + 15, 3, color);
    }
}

/**
 * @brief 绘制触摸按钮
 */
void NavPaging_DrawTouchButtons(void)
{
    uint16_t y_pos = LCD_H - 35;

    // 上一页按钮
    if (g_nav_paging.current_page > 0) {
        LCD_DrawRectangle(5, y_pos, 55, y_pos + 30, WHITE);
        LCD_ShowString(15, y_pos + 8, (const uint8_t*)"<PREV", WHITE, BLACK, 12, 0);
    }

    // 下一页按钮
    if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
        LCD_DrawRectangle(LCD_W - 55, y_pos, LCD_W - 5, y_pos + 30, WHITE);
        LCD_ShowString(LCD_W - 45, y_pos + 8, (const uint8_t*)"NEXT>", WHITE, BLACK, 12, 0);
    }

    // 返回按钮
    LCD_DrawRectangle(LCD_W/2 - 25, y_pos, LCD_W/2 + 25, y_pos + 30, GRAY);
    LCD_ShowString(LCD_W/2 - 15, y_pos + 8, (const uint8_t*)"HOME", WHITE, BLACK, 12, 0);
}
