/**
 * 分页导航显示系统实现
 */

#include "navigation_paging.h"
#include "navigation_app.h"  // 添加导航应用头文件
//#include "lcd_driver.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 全局变量
NavigationPaging_t g_nav_paging = {0};
static NavigationStep_t g_nav_steps[20] = {0}; // 最多20个导航步骤
static NavigationPageType_t g_current_page_type = NAV_PAGE_STEPS;

/**
 * @brief 初始化分页导航系统
 */
void NavPaging_Init(void)
{
    // 初始化分页状态
    g_nav_paging.steps = g_nav_steps;
    g_nav_paging.total_steps = 0;
    g_nav_paging.current_page = 0;      // 确保从第一页开始
    g_nav_paging.steps_per_page = 4;    // 每页显示4个步骤
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_step = 1;      // 从第1步开始
    g_nav_paging.total_distance = 0.0f;
    g_nav_paging.estimated_time = 0;

    // 加载动态路线（从导航系统获取实时数据）
    NavPaging_LoadDynamicRoute();
}

/**
 * @brief 加载默认路线
 */
void NavPaging_LoadDefaultRoute(void)
{
    g_nav_paging.total_distance = 0.0f;
    g_nav_paging.estimated_time = 0;

    NavPaging_AddStep(1, "Unknown destination", "0 m", "", 0);
}

/**
 * @brief 从检测器实时获取动态导航数据
 */
void NavPaging_LoadDynamicRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 检查导航系统是否激活
    extern Navigation_t current_navigation;
    extern NavigationState_t nav_state;

    if (nav_state == NAV_STATE_IDLE || !current_navigation.is_active) {
        // 显示空闲状态
        NavPaging_LoadIdleState();
        return;
    }

    // 实时从检测器获取导航数据
    NavPaging_FetchDetectorData();

    // 重新计算页数
    g_nav_paging.steps_per_page = 5;  // 检测器显示5个步骤
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;  // 从第一页开始

    // 更新当前步骤（基于GPS位置）
    NavPaging_UpdateCurrentStep();
}

/**
 * @brief 实时从检测器获取导航数据
 */
void NavPaging_FetchDetectorData(void)
{
    extern Navigation_t current_navigation;
    const char* dest_name = current_navigation.destination.name;

    // 根据目的地获取检测器实时数据
    if (strcmp(dest_name, "wanda") == 0 || strcmp(dest_name, "wangda") == 0) {
        NavPaging_FetchWandaData();
    } else if (strcmp(dest_name, "shuyuan") == 0) {
        NavPaging_FetchAcademyData();
    } else if (strcmp(dest_name, "tiyuzhonxin") == 0) {
        NavPaging_FetchSportsData();
    } else if (strcmp(dest_name, "huochezhan") == 0) {
        NavPaging_FetchTrainData();
    } else if (strcmp(dest_name, "yiyuan") == 0) {
        NavPaging_FetchHospitalData();
    } else {
        NavPaging_LoadDefaultRoute();
    }
}

/**
 * @brief 实时获取万达广场导航数据（从检测器）
 */
void NavPaging_FetchWandaData(void)
{
    // 实时从检测器获取数据
    NavigationDetectorData_t detector_data;
    if (NavPaging_RequestDetectorData("wanda", &detector_data)) {
        // 使用检测器返回的实时数据
        g_nav_paging.total_distance = detector_data.total_distance;
        g_nav_paging.estimated_time = detector_data.estimated_time;

        // 添加检测器返回的步骤
        for (int i = 0; i < detector_data.step_count && i < 20; i++) {
            NavPaging_AddStep(i + 1,
                            detector_data.steps[i].instruction,
                            detector_data.steps[i].distance,
                            "",
                            detector_data.steps[i].direction);
        }
    } else {
        // 检测器无响应时使用默认数据
        g_nav_paging.total_distance = 3.78f;
        g_nav_paging.estimated_time = 4;

        NavPaging_AddStep(1, "Start from Lingtai Road", "1.1 km", "", 0);
        NavPaging_AddStep(2, "Turn left onto Hengzhou Ave", "2.0 km", "", 1);
        NavPaging_AddStep(3, "Continue on Hengzhou Ave", "601 m", "", 0);
        NavPaging_AddStep(4, "Turn right onto Jiangxiang Rd", "119 m", "", 2);
        NavPaging_AddStep(5, "Arrive at destination", "0 m", "", 3);
    }
}

/**
 * @brief 加载万达广场检测器数据（完全按照检测器显示）
 */
void NavPaging_LoadWandaDetectorData(void)
{
    // 设置路线信息（完全按照检测器显示）
    g_nav_paging.total_distance = 3.78f;  // 3.78 km
    g_nav_paging.estimated_time = 4;      // 4分钟

    // 添加检测器上显示的5个步骤（完全一致）
    NavPaging_AddStep(1, "Start from Lingtai Road", "1.1 km", "", 0);
    NavPaging_AddStep(2, "Turn left onto Hengzhou Ave", "2.0 km", "", 1);
    NavPaging_AddStep(3, "Continue on Hengzhou Ave", "601 m", "", 0);
    NavPaging_AddStep(4, "Turn right onto Jiangxiang Rd", "119 m", "", 2);
    NavPaging_AddStep(5, "Arrive at destination", "0 m", "", 3);
}

/**
 * @brief 实时获取酃湖书院导航数据
 */
void NavPaging_FetchAcademyData(void)
{
    // TODO: 从检测器实时获取数据
    g_nav_paging.total_distance = 2.5f;  // 模拟数据，应从检测器获取
    g_nav_paging.estimated_time = 3;

    NavPaging_AddStep(1, "Start from current location", "800 m", "", 0);
    NavPaging_AddStep(2, "Turn right onto main road", "1.2 km", "", 2);
    NavPaging_AddStep(3, "Continue straight", "500 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Linghu Academy", "0 m", "", 3);
}

/**
 * @brief 实时获取体育中心导航数据
 */
void NavPaging_FetchSportsData(void)
{
    // TODO: 从检测器实时获取数据
    g_nav_paging.total_distance = 4.2f;
    g_nav_paging.estimated_time = 5;

    NavPaging_AddStep(1, "Start from current location", "1.5 km", "", 0);
    NavPaging_AddStep(2, "Turn left onto Hengzhou Ave", "2.0 km", "", 1);
    NavPaging_AddStep(3, "Continue straight", "700 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Sports Center", "0 m", "", 3);
}

/**
 * @brief 实时获取火车站导航数据
 */
void NavPaging_FetchTrainData(void)
{
    // TODO: 从检测器实时获取数据
    g_nav_paging.total_distance = 5.1f;
    g_nav_paging.estimated_time = 6;

    NavPaging_AddStep(1, "Start from current location", "2.0 km", "", 0);
    NavPaging_AddStep(2, "Turn right onto main road", "2.5 km", "", 2);
    NavPaging_AddStep(3, "Continue to train station", "600 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Train Station", "0 m", "", 3);
}

/**
 * @brief 实时获取医院导航数据
 */
void NavPaging_FetchHospitalData(void)
{
    // TODO: 从检测器实时获取数据
    g_nav_paging.total_distance = 3.2f;
    g_nav_paging.estimated_time = 4;

    NavPaging_AddStep(1, "Start from current location", "1.0 km", "", 0);
    NavPaging_AddStep(2, "Turn left onto hospital road", "1.8 km", "", 1);
    NavPaging_AddStep(3, "Continue straight", "400 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Hospital", "0 m", "", 3);
}

/**
 * @brief 加载体育中心检测器数据
 */
void NavPaging_LoadSportsDetectorData(void)
{
    g_nav_paging.total_distance = 4.2f;
    g_nav_paging.estimated_time = 5;

    NavPaging_AddStep(1, "Start from current location", "1.5 km", "", 0);
    NavPaging_AddStep(2, "Turn left onto Hengzhou Ave", "2.0 km", "", 1);
    NavPaging_AddStep(3, "Continue straight", "700 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Sports Center", "0 m", "", 3);
}

/**
 * @brief 加载火车站检测器数据
 */
void NavPaging_LoadTrainDetectorData(void)
{
    g_nav_paging.total_distance = 5.1f;
    g_nav_paging.estimated_time = 6;

    NavPaging_AddStep(1, "Start from current location", "2.0 km", "", 0);
    NavPaging_AddStep(2, "Turn right onto main road", "2.5 km", "", 2);
    NavPaging_AddStep(3, "Continue to train station", "600 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Train Station", "0 m", "", 3);
}

/**
 * @brief 加载医院检测器数据
 */
void NavPaging_LoadHospitalDetectorData(void)
{
    g_nav_paging.total_distance = 3.2f;
    g_nav_paging.estimated_time = 4;

    NavPaging_AddStep(1, "Start from current location", "1.0 km", "", 0);
    NavPaging_AddStep(2, "Turn left onto hospital road", "1.8 km", "", 1);
    NavPaging_AddStep(3, "Continue straight", "400 m", "", 0);
    NavPaging_AddStep(4, "Arrive at Hospital", "0 m", "", 3);
}

/**
 * @brief 显示空闲状态
 */
void NavPaging_LoadIdleState(void)
{
    g_nav_paging.total_distance = 0.0f;
    g_nav_paging.estimated_time = 0;

    // 添加空闲状态信息（英文）
    NavPaging_AddStep(1, "Navigation System Ready", "0 m", "", 0);
    NavPaging_AddStep(2, "Enter nav_test1-5 to start", "0 m", "", 0);
    NavPaging_AddStep(3, "Type 'help' for commands", "0 m", "", 0);

    g_nav_paging.steps_per_page = 5;
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 更新当前步骤（基于GPS位置和实时进度）
 */
void NavPaging_UpdateCurrentStep(void)
{
    extern Navigation_t current_navigation;

    // 获取当前GPS位置
    float current_lat, current_lon, current_alt;
    extern void esp01_GetRealLocation(float *lat, float *lon, float *alt);
    esp01_GetRealLocation(&current_lat, &current_lon, &current_alt);

    if (current_lat == 0.0f || current_lon == 0.0f) {
        g_nav_paging.current_step = 1; // GPS无效时默认第一步
        return;
    }

    // 计算到目的地的距离
    float distance_to_dest = NavPaging_CalculateDistance(
        current_lat, current_lon,
        current_navigation.destination.latitude,
        current_navigation.destination.longitude
    );

    // 智能步骤判断（基于实际距离和检测器数据）
    uint8_t new_step = NavPaging_CalculateCurrentStepFromDistance(distance_to_dest);

    // 如果步骤发生变化，标记之前的步骤为已完成
    if (new_step > g_nav_paging.current_step) {
        for (uint8_t i = g_nav_paging.current_step; i < new_step; i++) {
            NavPaging_CompleteStep(i);
        }
    }

    g_nav_paging.current_step = new_step;

    // 确保步骤在有效范围内
    if (g_nav_paging.current_step < 1) g_nav_paging.current_step = 1;
    if (g_nav_paging.current_step > g_nav_paging.total_steps) g_nav_paging.current_step = g_nav_paging.total_steps;
}

/**
 * @brief 根据剩余距离计算当前步骤
 */
uint8_t NavPaging_CalculateCurrentStepFromDistance(float distance_to_dest)
{
    // 根据检测器数据的距离阈值判断当前步骤
    if (distance_to_dest < 50.0f) {
        return g_nav_paging.total_steps; // 即将到达
    } else if (distance_to_dest < 200.0f) {
        return g_nav_paging.total_steps - 1; // 最后转弯
    } else if (distance_to_dest < 800.0f) {
        return g_nav_paging.total_steps - 2; // 主要道路后段
    } else if (distance_to_dest < 2500.0f) {
        return 2; // 主要道路前段
    } else {
        return 1; // 起始段
    }
}

/**
 * @brief 计算两点间距离（米）
 */
float NavPaging_CalculateDistance(float lat1, float lon1, float lat2, float lon2)
{
    const float R = 6371000; // 地球半径（米）
    float dLat = (lat2 - lat1) * M_PI / 180.0f;
    float dLon = (lon2 - lon1) * M_PI / 180.0f;
    float a = sin(dLat/2) * sin(dLat/2) + cos(lat1 * M_PI / 180.0f) * cos(lat2 * M_PI / 180.0f) * sin(dLon/2) * sin(dLon/2);
    float c = 2 * atan2(sqrt(a), sqrt(1-a));
    return R * c;
}

/**
 * @brief 模拟检测器实时数据获取（实际应该从检测器API获取）
 */
uint8_t NavPaging_GetDetectorRealTimeData(const char* destination, NavigationDetectorData_t* data)
{
    // TODO: 实现真实的检测器API调用
    // 这里应该调用检测器的实时API获取当前导航数据

    // 模拟检测器API调用
    extern void esp01_UploadNavigationCommand(int field3_value);

    // 根据目的地发送对应的field3值
    int field3_value = 9999;
    if (strcmp(destination, "wanda") == 0) {
        field3_value = 1111;
    } else if (strcmp(destination, "shuyuan") == 0) {
        field3_value = 2222;
    } else if (strcmp(destination, "tiyuzhonxin") == 0) {
        field3_value = 3333;
    } else if (strcmp(destination, "huochezhan") == 0) {
        field3_value = 4444;
    } else if (strcmp(destination, "yiyuan") == 0) {
        field3_value = 5555;
    }

    // 发送到检测器
    esp01_UploadNavigationCommand(field3_value);

    // TODO: 等待检测器响应并解析数据
    // 目前返回失败，使用默认数据
    return 0;
}

/**
 * @brief 请求检测器导航数据
 */
uint8_t NavPaging_RequestDetectorData(const char* destination, NavigationDetectorData_t* data)
{
    // TODO: 实现与检测器的实时通信
    // 这里应该通过UART/ESP01向检测器发送请求并解析响应

    // 模拟检测器通信协议
    char request_cmd[64];
    snprintf(request_cmd, sizeof(request_cmd), "GET_NAV_DATA:%s", destination);

    // 发送请求到检测器（通过ESP01或其他通信方式）
    // esp01_SendCommand(request_cmd);

    // 等待并解析检测器响应
    // char response[512];
    // if (esp01_ReceiveResponse(response, 2000)) {  // 2秒超时
    //     return NavPaging_ParseDetectorResponse(response, data);
    // }

    // 目前返回失败，使用默认数据
    return 0;
}

/**
 * @brief 解析检测器响应数据
 */
uint8_t NavPaging_ParseDetectorResponse(const char* response, NavigationDetectorData_t* data)
{
    // TODO: 解析检测器返回的JSON或其他格式数据
    // 示例格式：
    // {
    //   "destination": "Wanda Plaza",
    //   "total_distance": 3.78,
    //   "estimated_time": 4,
    //   "steps": [
    //     {"instruction": "Start from Lingtai Road", "distance": "1.1 km", "direction": 0},
    //     {"instruction": "Turn left onto Hengzhou Ave", "distance": "2.0 km", "direction": 1},
    //     ...
    //   ]
    // }

    // 这里应该实现JSON解析或其他协议解析
    return 0;
}

/**
 * @brief 根据方位角确定方向
 */
uint8_t NavPaging_GetDirectionFromBearing(float bearing)
{
    // 根据方位角确定转向方向
    if (bearing >= 315.0f || bearing < 45.0f) {
        return 0; // 直行/北
    } else if (bearing >= 45.0f && bearing < 135.0f) {
        return 2; // 右转/东
    } else if (bearing >= 135.0f && bearing < 225.0f) {
        return 0; // 直行/南
    } else {
        return 1; // 左转/西
    }
}

/**
 * @brief 添加导航步骤
 */
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance, 
                       const char* road_name, uint8_t direction)
{
    if (g_nav_paging.total_steps < 20) {
        NavigationStep_t* step = &g_nav_steps[g_nav_paging.total_steps];
        step->step_num = step_num;
        strncpy(step->instruction, instruction, sizeof(step->instruction) - 1);
        strncpy(step->distance, distance, sizeof(step->distance) - 1);
        strncpy(step->road_name, road_name, sizeof(step->road_name) - 1);
        step->direction = direction;
        step->completed = 0;
        
        g_nav_paging.total_steps++;
    }
}

/**
 * @brief 下一页
 */
void NavPaging_NextPage(void)
{
    if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
        g_nav_paging.current_page++;
    }
}

/**
 * @brief 上一页
 */
void NavPaging_PrevPage(void)
{
    if (g_nav_paging.current_page > 0) {
        g_nav_paging.current_page--;
    }
}

/**
 * @brief 设置当前步骤
 */
void NavPaging_SetCurrentStep(uint8_t step_num)
{
    g_nav_paging.current_step = step_num;
    
    // 自动跳转到包含当前步骤的页面
    if (step_num > 0) {
        uint8_t target_page = (step_num - 1) / g_nav_paging.steps_per_page;
        g_nav_paging.current_page = target_page;
    }
}

/**
 * @brief 完成步骤
 */
void NavPaging_CompleteStep(uint8_t step_num)
{
    if (step_num <= g_nav_paging.total_steps) {
        g_nav_steps[step_num - 1].completed = 1;
    }
}

/**
 * @brief 获取每页显示的步骤数
 */
uint8_t NavPaging_GetStepsPerPage(void)
{
    return g_nav_paging.steps_per_page;
}

/**
 * @brief 动态更新导航数据
 */
void NavPaging_UpdateDynamicData(void)
{
    // 重新加载导航数据
    NavPaging_LoadDynamicRoute();
}

/**
 * @brief 自动翻页处理（每10秒翻页一次，只有多页时才翻页）
 */
void NavPaging_AutoFlip(void)
{
    // 只有多页时才需要自动翻页
    if (g_nav_paging.total_pages <= 1) {
        return;
    }

    static uint32_t last_flip_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每10秒自动翻页
    if (current_time - last_flip_time >= 10000) {
        last_flip_time = current_time;

        // 自动翻到下一页，如果是最后一页则回到第一页
        if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
            g_nav_paging.current_page++;
        } else {
            g_nav_paging.current_page = 0; // 循环回到第一页
        }
    }
}

/**
 * @brief 主显示函数
 */
void NavPaging_Display(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);

    // 绘制头部
    NavPaging_DrawHeader();

    // 绘制导航步骤
    NavPaging_DrawSteps();

    // 绘制页面指示器
    NavPaging_DrawPageIndicator();

    // 绘制自动翻页提示
    NavPaging_DrawAutoFlipInfo();
}

/**
 * @brief 绘制头部信息（英文界面，动态数据）
 */
void NavPaging_DrawHeader(void)
{
    char buffer[64];
    extern Navigation_t current_navigation;
    extern NavigationState_t nav_state;

    // 标题（完全使用英文）
    if (nav_state == NAV_STATE_IDLE || !current_navigation.is_active) {
        LCD_ShowString(10, 5, (const uint8_t*)"Navigation Info", WHITE, BLACK, 16, 0);
    } else {
        // 显示目的地信息（英文）
        const char* dest_name = current_navigation.destination.name;
        if (strcmp(dest_name, "wanda") == 0 || strcmp(dest_name, "wangda") == 0) {
            LCD_ShowString(10, 5, (const uint8_t*)"To: Wanda Plaza", WHITE, BLACK, 14, 0);
        } else if (strcmp(dest_name, "shuyuan") == 0) {
            LCD_ShowString(10, 5, (const uint8_t*)"To: Linghu Academy", WHITE, BLACK, 14, 0);
        } else if (strcmp(dest_name, "tiyuzhonxin") == 0) {
            LCD_ShowString(10, 5, (const uint8_t*)"To: Sports Center", WHITE, BLACK, 14, 0);
        } else if (strcmp(dest_name, "huochezhan") == 0) {
            LCD_ShowString(10, 5, (const uint8_t*)"To: Train Station", WHITE, BLACK, 14, 0);
        } else if (strcmp(dest_name, "yiyuan") == 0) {
            LCD_ShowString(10, 5, (const uint8_t*)"To: Hospital", WHITE, BLACK, 14, 0);
        } else {
            LCD_ShowString(10, 5, (const uint8_t*)"Navigation Active", WHITE, BLACK, 14, 0);
        }
    }

    // 第一行信息：距离、时间、状态
    if (g_nav_paging.total_distance > 0) {
        snprintf(buffer, sizeof(buffer), "%.2f km", g_nav_paging.total_distance);
        LCD_ShowString(10, 30, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

        snprintf(buffer, sizeof(buffer), "%d min", g_nav_paging.estimated_time);
        LCD_ShowString(100, 30, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

        LCD_ShowString(180, 30, (const uint8_t*)"ACTIVE", GREEN, BLACK, 16, 0);
    } else {
        LCD_ShowString(10, 30, (const uint8_t*)"0.0 km", GRAY, BLACK, 16, 0);
        LCD_ShowString(100, 30, (const uint8_t*)"0 min", GRAY, BLACK, 16, 0);
        LCD_ShowString(180, 30, (const uint8_t*)"IDLE", GRAY, BLACK, 16, 0);
    }

    // 第二行标签（英文）
    LCD_ShowString(10, 50, (const uint8_t*)"Distance", GRAY, BLACK, 12, 0);
    LCD_ShowString(100, 50, (const uint8_t*)"Time", GRAY, BLACK, 12, 0);
    LCD_ShowString(180, 50, (const uint8_t*)"Status", GRAY, BLACK, 12, 0);

    // 分隔线
    LCD_DrawLine(5, 70, LCD_W - 5, 70, WHITE);
}

/**
 * @brief 绘制导航步骤
 */
void NavPaging_DrawSteps(void)
{
    uint16_t y_start = 80;  // 调整起始位置，为头部留出更多空间
    uint16_t step_height = 40;  // 增加步骤高度，适应中文显示
    uint8_t start_step = g_nav_paging.current_page * g_nav_paging.steps_per_page;
    uint8_t end_step = start_step + g_nav_paging.steps_per_page;

    if (end_step > g_nav_paging.total_steps) {
        end_step = g_nav_paging.total_steps;
    }

    for (uint8_t i = start_step; i < end_step; i++) {
        NavigationStep_t* step = &g_nav_steps[i];
        uint16_t y_pos = y_start + (i - start_step) * step_height;

        // 确定步骤颜色
        uint16_t step_color = GREEN;
        if (step->completed) {
            step_color = GRAY;
        } else if (step->step_num == g_nav_paging.current_step) {
            step_color = YELLOW;
        }

        // 绘制步骤
        NavPaging_DrawSingleStep(step, y_pos, step_color);
    }
}

/**
 * @brief 绘制单个导航步骤
 */
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color)
{
    char buffer[8];

    // 根据步骤状态确定颜色
    uint16_t circle_color, text_color, distance_color, line_color, arrow_color;

    if (step->step_num < g_nav_paging.current_step) {
        // 已完成的步骤 - 绿色
        circle_color = GREEN;
        text_color = GREEN;
        distance_color = GREEN;
        line_color = GREEN;
        arrow_color = GREEN;
    } else if (step->step_num == g_nav_paging.current_step) {
        // 当前步骤 - 黄色高亮
        circle_color = YELLOW;
        text_color = WHITE;
        distance_color = YELLOW;
        line_color = YELLOW;
        arrow_color = YELLOW;
    } else {
        // 未来步骤 - 青色
        circle_color = CYAN;
        text_color = GRAY;
        distance_color = CYAN;
        line_color = CYAN;
        arrow_color = CYAN;
    }

    // 绘制步骤编号圆圈
    Draw_Circle(20, y_pos + 12, 10, circle_color);
    if (step->step_num < 10) {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(17, y_pos + 8, (const uint8_t*)buffer, BLACK, circle_color, 12, 0);
    } else {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(14, y_pos + 8, (const uint8_t*)buffer, BLACK, circle_color, 10, 0);
    }

    // 绘制连接线（除了最后一步）
    if (step->step_num < g_nav_paging.total_steps) {
        LCD_DrawLine(20, y_pos + 22, 20, y_pos + 45, line_color);
        LCD_DrawLine(19, y_pos + 22, 19, y_pos + 45, line_color);
        LCD_DrawLine(21, y_pos + 22, 21, y_pos + 45, line_color);
    }

    // 绘制导航指令（根据状态使用不同颜色）
    LCD_ShowString(40, y_pos + 5, (const uint8_t*)step->instruction, text_color, BLACK, 11, 0);
    LCD_ShowString(40, y_pos + 22, (const uint8_t*)step->distance, distance_color, BLACK, 11, 0);

    // 绘制改进的方向箭头
    NavPaging_DrawImprovedArrow(step->direction, 200, y_pos + 15, arrow_color);
}

/**
 * @brief 绘制改进的方向箭头（更清晰的显示）
 */
void NavPaging_DrawImprovedArrow(uint8_t direction, uint16_t x, uint16_t y, uint16_t color)
{
    switch (direction) {
        case 0: // 直行/出发 - 改进的向上箭头
            // 箭头头部
            LCD_DrawLine(x+8, y-10, x+4, y-6, color);
            LCD_DrawLine(x+8, y-10, x+12, y-6, color);
            LCD_DrawLine(x+8, y-9, x+5, y-5, color);
            LCD_DrawLine(x+8, y-9, x+11, y-5, color);

            // 箭头主体（粗线）
            LCD_DrawLine(x+8, y-10, x+8, y+10, color);
            LCD_DrawLine(x+7, y-8, x+7, y+10, color);
            LCD_DrawLine(x+9, y-8, x+9, y+10, color);
            LCD_DrawLine(x+6, y-6, x+6, y+8, color);
            LCD_DrawLine(x+10, y-6, x+10, y+8, color);
            break;

        case 1: // 左转 - 改进的向左箭头
            // 箭头头部
            LCD_DrawLine(x-2, y, x+2, y-4, color);
            LCD_DrawLine(x-2, y, x+2, y+4, color);
            LCD_DrawLine(x-1, y, x+3, y-3, color);
            LCD_DrawLine(x-1, y, x+3, y+3, color);

            // 箭头主体（粗线）
            LCD_DrawLine(x-2, y, x+14, y, color);
            LCD_DrawLine(x, y-1, x+14, y-1, color);
            LCD_DrawLine(x, y+1, x+14, y+1, color);
            LCD_DrawLine(x+2, y-2, x+12, y-2, color);
            LCD_DrawLine(x+2, y+2, x+12, y+2, color);
            break;

        case 2: // 右转 - 改进的向右箭头
            // 箭头头部
            LCD_DrawLine(x+14, y, x+10, y-4, color);
            LCD_DrawLine(x+14, y, x+10, y+4, color);
            LCD_DrawLine(x+13, y, x+9, y-3, color);
            LCD_DrawLine(x+13, y, x+9, y+3, color);

            // 箭头主体（粗线）
            LCD_DrawLine(x-2, y, x+14, y, color);
            LCD_DrawLine(x-2, y-1, x+12, y-1, color);
            LCD_DrawLine(x-2, y+1, x+12, y+1, color);
            LCD_DrawLine(x, y-2, x+10, y-2, color);
            LCD_DrawLine(x, y+2, x+10, y+2, color);
            break;

        case 3: // 到达目的地 - 改进的目标标记
            // 外圈
            Draw_Circle(x+8, y, 8, color);
            Draw_Circle(x+8, y, 7, color);
            // 内圈
            Draw_Circle(x+8, y, 5, RED);
            Draw_Circle(x+8, y, 4, RED);
            Draw_Circle(x+8, y, 3, RED);
            // 中心点
            Draw_Circle(x+8, y, 2, WHITE);
            break;
    }
}

/**
 * @brief 绘制方向箭头（兼容旧版本）
 */
void NavPaging_DrawDirectionArrow(uint8_t direction, uint16_t x, uint16_t y)
{
    NavPaging_DrawImprovedArrow(direction, x, y, YELLOW);
}

/**
 * @brief 绘制页面指示器（英文界面）
 */
void NavPaging_DrawPageIndicator(void)
{
    char buffer[32];
    uint16_t y_pos = LCD_H - 50;

    // 页面信息（使用英文）
    if (g_nav_paging.total_pages > 1) {
        snprintf(buffer, sizeof(buffer), "Page %d/%d", g_nav_paging.current_page + 1, g_nav_paging.total_pages);
        LCD_ShowString(LCD_W/2 - 30, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);

        // 页面点指示器
        uint16_t dot_x = LCD_W/2 - g_nav_paging.total_pages * 6;
        for (uint8_t i = 0; i < g_nav_paging.total_pages; i++) {
            uint16_t color = (i == g_nav_paging.current_page) ? CYAN : GRAY;
            Draw_Circle(dot_x + i * 12, y_pos + 18, 3, color);
        }
    } else {
        // 单页时显示步骤信息
        snprintf(buffer, sizeof(buffer), "Steps: %d", g_nav_paging.total_steps);
        LCD_ShowString(LCD_W/2 - 30, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);
    }
}

/**
 * @brief 绘制自动翻页信息（英文界面）
 */
void NavPaging_DrawAutoFlipInfo(void)
{
    uint16_t y_pos = LCD_H - 25;
    extern NavigationState_t nav_state;

    // 显示状态信息（使用英文）
    if (nav_state == NAV_STATE_IDLE) {
        LCD_ShowString(10, y_pos, (const uint8_t*)"Ready for navigation", GRAY, BLACK, 10, 0);
    } else if (g_nav_paging.total_pages > 1) {
        LCD_ShowString(10, y_pos, (const uint8_t*)"Auto flip every 10s", GRAY, BLACK, 10, 0);
    } else {
        LCD_ShowString(10, y_pos, (const uint8_t*)"Navigation active", GREEN, BLACK, 10, 0);
    }
}
