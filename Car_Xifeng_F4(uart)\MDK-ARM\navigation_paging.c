/**
 * 分页导航显示系统实现
 */

#include "navigation_paging.h"

// 全局变量
NavigationPaging_t g_nav_paging = {0};
static NavigationStep_t g_nav_steps[20] = {0}; // 最多20个导航步骤
static NavigationPageType_t g_current_page_type = NAV_PAGE_STEPS;

/**
 * @brief 初始化分页导航系统
 */
void NavPaging_Init(void)
{
    // 初始化分页状态
    g_nav_paging.steps = g_nav_steps;
    g_nav_paging.total_steps = 0;
    g_nav_paging.current_page = 0;      // 确保从第一页开始
    g_nav_paging.steps_per_page = 4;    // 每页显示4个步骤
    g_nav_paging.total_pages = 1;
    g_nav_paging.current_step = 1;      // 从第1步开始
    g_nav_paging.total_distance = 3.78f;
    g_nav_paging.estimated_time = 4;    // 与检测器一致：4分钟

    // 加载万达路线（与检测器信息一致）
    NavPaging_LoadWandaRoute();
}

/**
 * @brief 加载万达路线（与检测器信息一致）
 */
void NavPaging_LoadWandaRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息（与检测器一致）
    g_nav_paging.total_distance = 3.78f;  // 3.78 km
    g_nav_paging.estimated_time = 4;      // 4分钟

    // 添加与检测器完全一致的5个导航步骤
    NavPaging_AddStep(1, "从陵台路出发", "1.1公里", "陵台路", 0);
    NavPaging_AddStep(2, "左转进入衡州大道", "2.0公里", "衡州大道", 1);
    NavPaging_AddStep(3, "继续沿衡州大道直行", "601米", "衡州大道", 0);
    NavPaging_AddStep(4, "右转进入蒋翔路", "119米", "蒋翔路", 2);
    NavPaging_AddStep(5, "到达目的地", "0米", "万达广场", 3);

    // 重新计算页数（5个步骤，每页4个，需要2页）
    g_nav_paging.steps_per_page = 4;
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;  // 从第一页开始
    g_nav_paging.current_step = 1;
}

/**
 * @brief 添加导航步骤
 */
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance, 
                       const char* road_name, uint8_t direction)
{
    if (g_nav_paging.total_steps < 20) {
        NavigationStep_t* step = &g_nav_steps[g_nav_paging.total_steps];
        step->step_num = step_num;
        strncpy(step->instruction, instruction, sizeof(step->instruction) - 1);
        strncpy(step->distance, distance, sizeof(step->distance) - 1);
        strncpy(step->road_name, road_name, sizeof(step->road_name) - 1);
        step->direction = direction;
        step->completed = 0;
        
        g_nav_paging.total_steps++;
    }
}

/**
 * @brief 下一页
 */
void NavPaging_NextPage(void)
{
    if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
        g_nav_paging.current_page++;
    }
}

/**
 * @brief 上一页
 */
void NavPaging_PrevPage(void)
{
    if (g_nav_paging.current_page > 0) {
        g_nav_paging.current_page--;
    }
}

/**
 * @brief 设置当前步骤
 */
void NavPaging_SetCurrentStep(uint8_t step_num)
{
    g_nav_paging.current_step = step_num;
    
    // 自动跳转到包含当前步骤的页面
    if (step_num > 0) {
        uint8_t target_page = (step_num - 1) / g_nav_paging.steps_per_page;
        g_nav_paging.current_page = target_page;
    }
}

/**
 * @brief 完成步骤
 */
void NavPaging_CompleteStep(uint8_t step_num)
{
    if (step_num <= g_nav_paging.total_steps) {
        g_nav_steps[step_num - 1].completed = 1;
    }
}

/**
 * @brief 获取每页显示的步骤数
 */
uint8_t NavPaging_GetStepsPerPage(void)
{
    return g_nav_paging.steps_per_page;
}

/**
 * @brief 自动翻页处理（每10秒翻页一次，只有多页时才翻页）
 */
void NavPaging_AutoFlip(void)
{
    // 只有多页时才需要自动翻页
    if (g_nav_paging.total_pages <= 1) {
        return;
    }

    static uint32_t last_flip_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每10秒自动翻页
    if (current_time - last_flip_time >= 10000) {
        last_flip_time = current_time;

        // 自动翻到下一页，如果是最后一页则回到第一页
        if (g_nav_paging.current_page < g_nav_paging.total_pages - 1) {
            g_nav_paging.current_page++;
        } else {
            g_nav_paging.current_page = 0; // 循环回到第一页
        }
    }
}

/**
 * @brief 主显示函数
 */
void NavPaging_Display(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);

    // 绘制头部
    NavPaging_DrawHeader();

    // 绘制导航步骤
    NavPaging_DrawSteps();

    // 绘制页面指示器
    NavPaging_DrawPageIndicator();

    // 绘制自动翻页提示
    NavPaging_DrawAutoFlipInfo();
}

/**
 * @brief 绘制头部信息（与检测器界面一致）
 */
void NavPaging_DrawHeader(void)
{
    char buffer[64];

    // 标题（使用中文）
    LCD_ShowString(10, 5, (const uint8_t*)"导航信息", WHITE, BLACK, 16, 0);

    // 第一行信息：距离、时间、路径规划
    snprintf(buffer, sizeof(buffer), "%.2f km", g_nav_paging.total_distance);
    LCD_ShowString(10, 30, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

    snprintf(buffer, sizeof(buffer), "%d 分钟", g_nav_paging.estimated_time);
    LCD_ShowString(100, 30, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

    LCD_ShowString(180, 30, (const uint8_t*)"OSRM", GREEN, BLACK, 16, 0);

    // 第二行标签（与检测器一致）
    LCD_ShowString(10, 50, (const uint8_t*)"距离", GRAY, BLACK, 12, 0);
    LCD_ShowString(100, 50, (const uint8_t*)"预计时间", GRAY, BLACK, 12, 0);
    LCD_ShowString(180, 50, (const uint8_t*)"路径规划", GRAY, BLACK, 12, 0);

    // 分隔线
    LCD_DrawLine(5, 70, LCD_W - 5, 70, WHITE);
}

/**
 * @brief 绘制导航步骤
 */
void NavPaging_DrawSteps(void)
{
    uint16_t y_start = 80;  // 调整起始位置，为头部留出更多空间
    uint16_t step_height = 40;  // 增加步骤高度，适应中文显示
    uint8_t start_step = g_nav_paging.current_page * g_nav_paging.steps_per_page;
    uint8_t end_step = start_step + g_nav_paging.steps_per_page;

    if (end_step > g_nav_paging.total_steps) {
        end_step = g_nav_paging.total_steps;
    }

    for (uint8_t i = start_step; i < end_step; i++) {
        NavigationStep_t* step = &g_nav_steps[i];
        uint16_t y_pos = y_start + (i - start_step) * step_height;

        // 确定步骤颜色
        uint16_t step_color = GREEN;
        if (step->completed) {
            step_color = GRAY;
        } else if (step->step_num == g_nav_paging.current_step) {
            step_color = YELLOW;
        }

        // 绘制步骤
        NavPaging_DrawSingleStep(step, y_pos, step_color);
    }
}

/**
 * @brief 绘制单个导航步骤
 */
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color)
{
    char buffer[8];

    // 绘制步骤编号圆圈
    Draw_Circle(20, y_pos + 12, 10, color);
    if (step->step_num < 10) {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(17, y_pos + 8, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);
    } else {
        snprintf(buffer, sizeof(buffer), "%d", step->step_num);
        LCD_ShowString(14, y_pos + 8, (const uint8_t*)buffer, WHITE, BLACK, 10, 0);
    }

    // 绘制连接线（除了最后一步）
    if (step->step_num < g_nav_paging.total_steps) {
        LCD_DrawLine(20, y_pos + 22, 20, y_pos + 45, CYAN);
        LCD_DrawLine(19, y_pos + 22, 19, y_pos + 45, CYAN);
        LCD_DrawLine(21, y_pos + 22, 21, y_pos + 45, CYAN);
    }

    // 绘制导航指令（使用中文，与检测器一致）
    LCD_ShowString(40, y_pos + 5, (const uint8_t*)step->instruction, WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y_pos + 22, (const uint8_t*)step->distance, CYAN, BLACK, 12, 0);

    // 绘制方向箭头（调整位置）
    NavPaging_DrawDirectionArrow(step->direction, 200, y_pos + 15);
}

/**
 * @brief 绘制方向箭头（根据中文指令判断方向）
 */
void NavPaging_DrawDirectionArrow(uint8_t direction, uint16_t x, uint16_t y)
{
    switch (direction) {
        case 0: // 直行/出发
            // 直行箭头 ↑
            LCD_DrawLine(x+6, y-8, x+2, y-4, YELLOW);
            LCD_DrawLine(x+6, y-8, x+10, y-4, YELLOW);
            LCD_DrawLine(x+6, y-8, x+6, y+8, YELLOW);
            LCD_DrawLine(x+5, y-8, x+5, y+8, YELLOW);
            LCD_DrawLine(x+7, y-8, x+7, y+8, YELLOW);
            break;

        case 1: // 左转
            // 左转箭头 ←
            LCD_DrawLine(x, y, x+8, y-4, YELLOW);
            LCD_DrawLine(x, y, x+8, y+4, YELLOW);
            LCD_DrawLine(x, y, x+12, y, YELLOW);
            LCD_DrawLine(x, y-1, x+12, y-1, YELLOW);
            LCD_DrawLine(x, y+1, x+12, y+1, YELLOW);
            break;

        case 2: // 右转
            // 右转箭头 →
            LCD_DrawLine(x+12, y, x+4, y-4, YELLOW);
            LCD_DrawLine(x+12, y, x+4, y+4, YELLOW);
            LCD_DrawLine(x, y, x+12, y, YELLOW);
            LCD_DrawLine(x, y-1, x+12, y-1, YELLOW);
            LCD_DrawLine(x, y+1, x+12, y+1, YELLOW);
            break;

        case 3: // 到达目的地
            // 目的地标记 ●
            Draw_Circle(x+6, y, 6, RED);
            Draw_Circle(x+6, y, 5, RED);
            Draw_Circle(x+6, y, 4, RED);
            break;
    }
}

/**
 * @brief 绘制页面指示器
 */
void NavPaging_DrawPageIndicator(void)
{
    char buffer[32];
    uint16_t y_pos = LCD_H - 50;

    // 页面信息（使用中文）
    snprintf(buffer, sizeof(buffer), "第%d页/共%d页", g_nav_paging.current_page + 1, g_nav_paging.total_pages);
    LCD_ShowString(LCD_W/2 - 40, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);

    // 页面点指示器（只有多页时才显示）
    if (g_nav_paging.total_pages > 1) {
        uint16_t dot_x = LCD_W/2 - g_nav_paging.total_pages * 6;
        for (uint8_t i = 0; i < g_nav_paging.total_pages; i++) {
            uint16_t color = (i == g_nav_paging.current_page) ? CYAN : GRAY;
            Draw_Circle(dot_x + i * 12, y_pos + 18, 3, color);
        }
    }
}

/**
 * @brief 绘制自动翻页信息
 */
void NavPaging_DrawAutoFlipInfo(void)
{
    uint16_t y_pos = LCD_H - 25;

    // 显示自动翻页信息（使用中文）
    if (g_nav_paging.total_pages > 1) {
        LCD_ShowString(10, y_pos, (const uint8_t*)"每10秒自动翻页", GRAY, BLACK, 10, 0);
    } else {
        LCD_ShowString(10, y_pos, (const uint8_t*)"导航信息完整显示", GRAY, BLACK, 10, 0);
    }
}
