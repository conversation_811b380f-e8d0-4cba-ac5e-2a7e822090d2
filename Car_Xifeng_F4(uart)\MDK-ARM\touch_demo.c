/**
 * 触摸屏演示程序
 * 用于测试触摸功能和分页导航
 */

#include "touch_demo.h"
#include "navigation_paging.h"
#include "touch_driver.h"

/**
 * @brief 触摸屏校准演示
 */
void TouchDemo_Calibration(void)
{
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 显示校准说明
    LCD_ShowString(10, 10, (const uint8_t*)"Touch Calibration", WHITE, BLACK, 16, 0);
    LCD_ShowString(10, 40, (const uint8_t*)"Touch the corners:", YELLOW, BLACK, 12, 0);
    
    // 绘制四个角的校准点
    Draw_Circle(10, 10, 5, RED);
    Draw_Circle(LCD_W - 10, 10, 5, RED);
    Draw_Circle(10, LCD_H - 10, 5, RED);
    Draw_Circle(LCD_W - 10, LCD_H - 10, 5, RED);
    
    LCD_ShowString(10, LCD_H - 40, (const uint8_t*)"Touch any corner to continue", GRE<PERSON>, <PERSON><PERSON><PERSON><PERSON>, 12, 0);
}

/**
 * @brief 触摸区域测试
 */
void TouchDemo_AreaTest(void)
{
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 标题
    LCD_ShowString(10, 10, (const uint8_t*)"Touch Area Test", WHITE, BLACK, 16, 0);
    
    // 绘制测试区域
    LCD_DrawRectangle(20, 50, 100, 100, GREEN);
    LCD_ShowString(25, 70, (const uint8_t*)"Area 1", WHITE, BLACK, 12, 0);
    
    LCD_DrawRectangle(120, 50, 200, 100, BLUE);
    LCD_ShowString(125, 70, (const uint8_t*)"Area 2", WHITE, BLACK, 12, 0);
    
    LCD_DrawRectangle(20, 120, 100, 170, RED);
    LCD_ShowString(25, 140, (const uint8_t*)"Area 3", WHITE, BLACK, 12, 0);
    
    LCD_DrawRectangle(120, 120, 200, 170, YELLOW);
    LCD_ShowString(125, 140, (const uint8_t*)"Area 4", BLACK, BLACK, 12, 0);
    
    // 说明
    LCD_ShowString(10, 190, (const uint8_t*)"Touch areas to test", CYAN, BLACK, 12, 0);
    LCD_ShowString(10, 210, (const uint8_t*)"Swipe to change page", GRAY, BLACK, 12, 0);
}

/**
 * @brief 手势测试
 */
void TouchDemo_GestureTest(void)
{
    static TouchEvent_t last_event = TOUCH_EVENT_NONE;
    static uint32_t last_event_time = 0;
    
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 标题
    LCD_ShowString(10, 10, (const uint8_t*)"Gesture Test", WHITE, BLACK, 16, 0);
    
    // 获取当前触摸事件
    TouchState_t touch_state;
    if (Touch_Scan(&touch_state)) {
        last_event = touch_state.event;
        last_event_time = HAL_GetTick();
    }
    
    // 显示手势信息
    LCD_ShowString(10, 50, (const uint8_t*)"Last gesture:", YELLOW, BLACK, 12, 0);
    
    const char* event_names[] = {
        "NONE", "PRESS", "RELEASE", "MOVE",
        "SWIPE_UP", "SWIPE_DOWN", "SWIPE_LEFT", "SWIPE_RIGHT"
    };
    
    if (last_event < 8) {
        LCD_ShowString(10, 70, (const uint8_t*)event_names[last_event], GREEN, BLACK, 14, 0);
    }
    
    // 显示触摸坐标
    if (touch_state.current.valid) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "X:%d Y:%d", touch_state.current.x, touch_state.current.y);
        LCD_ShowString(10, 100, (const uint8_t*)buffer, CYAN, BLACK, 12, 0);
    }
    
    // 绘制手势指示
    LCD_ShowString(10, 130, (const uint8_t*)"Try these gestures:", WHITE, BLACK, 12, 0);
    LCD_ShowString(10, 150, (const uint8_t*)"- Swipe left/right", GRAY, BLACK, 10, 0);
    LCD_ShowString(10, 165, (const uint8_t*)"- Swipe up/down", GRAY, BLACK, 10, 0);
    LCD_ShowString(10, 180, (const uint8_t*)"- Tap areas", GRAY, BLACK, 10, 0);
    
    // 绘制手势区域
    LCD_DrawRectangle(50, 200, 190, 280, WHITE);
    LCD_ShowString(80, 235, (const uint8_t*)"Gesture Area", WHITE, BLACK, 12, 0);
}

/**
 * @brief 完整的触摸演示
 */
void TouchDemo_Complete(void)
{
    static uint8_t demo_mode = 0;
    static uint32_t last_switch = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 检查触摸事件切换模式
    TouchState_t touch_state;
    if (Touch_Scan(&touch_state)) {
        if (touch_state.event == TOUCH_EVENT_PRESS) {
            demo_mode = (demo_mode + 1) % 3;
            last_switch = current_time;
        }
    }
    
    // 自动切换模式（每5秒）
    if (current_time - last_switch > 5000) {
        demo_mode = (demo_mode + 1) % 3;
        last_switch = current_time;
    }
    
    switch (demo_mode) {
        case 0:
            TouchDemo_Calibration();
            break;
        case 1:
            TouchDemo_AreaTest();
            break;
        case 2:
            TouchDemo_GestureTest();
            break;
    }
}
