#include "uart_driver.h"

uint8_t uart_rx_dma_buffer[BUFFER_SIZE];
uint8_t ring_buffer_input[BUFFER_SIZE];
struct rt_ringbuffer ring_buffer;
uint8_t uart_data_buffer[BUFFER_SIZE];

int Uart_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 1. ȷ����Ŀ�괮�� (USART1)
    if (huart->Instance == USART1)
    {
        // 调试输出：DMA接收到数据 (使用GPIO指示) - 临时启用调试
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);

        // 2. ����ֹͣ��ǰ�� DMA ���� (������ڽ�����)
        //    ��Ϊ�����ж���ζ�ŷ��ͷ��Ѿ�ֹͣ����ֹ DMA �����ȴ������
        HAL_UART_DMAStop(huart);

        // 3. �� DMA ����������Ч������ (Size ���ֽ�) ���Ƶ�������������
        rt_ringbuffer_put(&ring_buffer, uart_rx_dma_buffer, Size);
        // ע�⣺����ʹ���� Size��ֻ����ʵ�ʽ��յ�������
        
        // 4. ����"����֪ͨ��"��������ѭ�������ݴ�����

        // 5. ��� DMA ���ջ�������Ϊ�´ν�����׼��
        //    ��Ȼ memcpy ֻ������ Size �������������������������
        memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

        // 6. 关键修复：完全重置UART和DMA状态
        huart->RxState = HAL_UART_STATE_READY;
        huart->hdmarx->State = HAL_DMA_STATE_READY;

        // 重新启动DMA接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

        // 7. 禁用半传输中断
        __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
    }
    // 处理串口2
    else if (huart->Instance == USART2)
    {
        // 调用串口2专用的回调函数
        HAL_UARTEx_RxEventCallback_UART2(huart, Size);
    }
    // 处理串口3
    else if (huart->Instance == USART3)
    {
        // 调用串口3专用的回调函数
        HAL_UARTEx_RxEventCallback_UART3(huart, Size);
    }
    // 处理串口6
    else if (huart->Instance == USART6)
    {
        // 调用串口6专用的回调函数
        HAL_UARTEx_RxEventCallback_UART6(huart, Size);
    }
}
