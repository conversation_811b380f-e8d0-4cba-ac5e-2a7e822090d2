/**
 * 导航路线数据定义
 * 包含完整的导航路线信息
 */

#ifndef __NAVIGATION_ROUTES_H__
#define __NAVIGATION_ROUTES_H__

#include "navigation_types.h"

// 预定义路线：衡阳师范学院到万达广场
typedef struct {
    const char* name;
    float total_distance;    // 总距离(km)
    uint16_t estimated_time; // 预计时间(分钟)
    uint8_t step_count;      // 步骤数量
} RouteInfo_t;

// 路线信息
extern const RouteInfo_t ROUTE_TO_WANDA;
extern const RouteInfo_t ROUTE_TO_ACADEMY;
extern const RouteInfo_t ROUTE_TO_SPORTS;
extern const RouteInfo_t ROUTE_TO_TRAIN;
extern const RouteInfo_t ROUTE_TO_HOSPITAL;

// 完整的导航步骤数据
extern const NavigationStep_t WANDA_ROUTE_STEPS[];
extern const NavigationStep_t ACADEMY_ROUTE_STEPS[];
extern const NavigationStep_t SPORTS_ROUTE_STEPS[];
extern const NavigationStep_t TRAIN_ROUTE_STEPS[];
extern const NavigationStep_t HOSPITAL_ROUTE_STEPS[];

// 函数声明
void NavRoutes_LoadWandaRoute(void);
void NavRoutes_LoadAcademyRoute(void);
void NavRoutes_LoadSportsRoute(void);
void NavRoutes_LoadTrainRoute(void);
void NavRoutes_LoadHospitalRoute(void);
void NavRoutes_LoadCustomRoute(const NavigationStep_t* steps, uint8_t count, float distance, uint16_t time);

#endif /* __NAVIGATION_ROUTES_H__ */
