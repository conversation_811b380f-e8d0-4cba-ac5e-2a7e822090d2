#include "lcd_display_hal.h"

/**
 * @brief 获取数字字符的像素点
 * @param digit 数字字符 ('0'-'9')
 * @param x,y 像素坐标
 * @param w,h 字符宽度和高度
 * @return 1-显示像素，0-背景
 */
uint8_t LCD_GetDigitPixel(uint8_t digit, uint8_t x, uint8_t y, uint8_t w, uint8_t h)
{
    // 简化的数字点阵 - 使用相对坐标
    uint8_t cx = x * 5 / w;  // 转换为5x7点阵坐标
    uint8_t cy = y * 7 / h;

    // 5x7数字点阵数据 (简化版)
    const uint8_t digit_patterns[10][7] = {
        // '0'
        {0x0E, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // '1'
        {0x04, 0x0C, 0x04, 0x04, 0x04, 0x04, 0x0E},
        // '2'
        {0x0E, 0x11, 0x01, 0x02, 0x04, 0x08, 0x1F},
        // '3'
        {0x0E, 0x11, 0x01, 0x06, 0x01, 0x11, 0x0E},
        // '4'
        {0x02, 0x06, 0x0A, 0x12, 0x1F, 0x02, 0x02},
        // '5'
        {0x1F, 0x10, 0x1E, 0x01, 0x01, 0x11, 0x0E},
        // '6'
        {0x06, 0x08, 0x10, 0x1E, 0x11, 0x11, 0x0E},
        // '7'
        {0x1F, 0x01, 0x02, 0x04, 0x08, 0x08, 0x08},
        // '8'
        {0x0E, 0x11, 0x11, 0x0E, 0x11, 0x11, 0x0E},
        // '9'
        {0x0E, 0x11, 0x11, 0x0F, 0x01, 0x02, 0x0C}
    };

    if(digit >= '0' && digit <= '9' && cy < 7 && cx < 5) {
        uint8_t pattern = digit_patterns[digit - '0'][cy];
        return (pattern >> (4 - cx)) & 0x01;
    }
    return 0;
}

/**
 * @brief 获取字母字符的像素点
 * @param letter 字母字符 ('A'-'Z')
 * @param x,y 像素坐标
 * @param w,h 字符宽度和高度
 * @return 1-显示像素，0-背景
 */
uint8_t LCD_GetLetterPixel(uint8_t letter, uint8_t x, uint8_t y, uint8_t w, uint8_t h)
{
    // 简化的字母点阵 - 使用相对坐标
    uint8_t cx = x * 5 / w;  // 转换为5x7点阵坐标
    uint8_t cy = y * 7 / h;

    // 5x7字母点阵数据 (部分字母)
    const uint8_t letter_patterns[26][7] = {
        // 'A'
        {0x04, 0x0A, 0x11, 0x11, 0x1F, 0x11, 0x11},
        // 'B'
        {0x1E, 0x11, 0x11, 0x1E, 0x11, 0x11, 0x1E},
        // 'C'
        {0x0E, 0x11, 0x10, 0x10, 0x10, 0x11, 0x0E},
        // 'D'
        {0x1C, 0x12, 0x11, 0x11, 0x11, 0x12, 0x1C},
        // 'E'
        {0x1F, 0x10, 0x10, 0x1E, 0x10, 0x10, 0x1F},
        // 'F'
        {0x1F, 0x10, 0x10, 0x1E, 0x10, 0x10, 0x10},
        // 'G'
        {0x0E, 0x11, 0x10, 0x17, 0x11, 0x11, 0x0F},
        // 'H'
        {0x11, 0x11, 0x11, 0x1F, 0x11, 0x11, 0x11},
        // 'I'
        {0x0E, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0E},
        // 'J'
        {0x07, 0x02, 0x02, 0x02, 0x02, 0x12, 0x0C},
        // 'K'
        {0x11, 0x12, 0x14, 0x18, 0x14, 0x12, 0x11},
        // 'L'
        {0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1F},
        // 'M'
        {0x11, 0x1B, 0x15, 0x15, 0x11, 0x11, 0x11},
        // 'N'
        {0x11, 0x19, 0x15, 0x13, 0x11, 0x11, 0x11},
        // 'O'
        {0x0E, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // 'P'
        {0x1E, 0x11, 0x11, 0x1E, 0x10, 0x10, 0x10},
        // 'Q'
        {0x0E, 0x11, 0x11, 0x11, 0x15, 0x12, 0x0D},
        // 'R'
        {0x1E, 0x11, 0x11, 0x1E, 0x14, 0x12, 0x11},
        // 'S'
        {0x0F, 0x10, 0x10, 0x0E, 0x01, 0x01, 0x1E},
        // 'T'
        {0x1F, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04},
        // 'U'
        {0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // 'V'
        {0x11, 0x11, 0x11, 0x11, 0x0A, 0x0A, 0x04},
        // 'W'
        {0x11, 0x11, 0x11, 0x15, 0x15, 0x1B, 0x11},
        // 'X'
        {0x11, 0x11, 0x0A, 0x04, 0x0A, 0x11, 0x11},
        // 'Y'
        {0x11, 0x11, 0x0A, 0x04, 0x04, 0x04, 0x04},
        // 'Z'
        {0x1F, 0x01, 0x02, 0x04, 0x08, 0x10, 0x1F}
    };

    if(letter >= 'A' && letter <= 'Z' && cy < 7 && cx < 5) {
        uint8_t pattern = letter_patterns[letter - 'A'][cy];
        return (pattern >> (4 - cx)) & 0x01;
    }
    return 0;
}

/**
 * @brief 在指定区域填充颜色
 * @param xsta,ysta   起始坐标
 * @param xend,yend   终止坐标
 * @param color       要填充的颜色
 */
void LCD_Fill(uint16_t xsta, uint16_t ysta, uint16_t xend, uint16_t yend, uint16_t color)
{          
    uint16_t i, j; 
    LCD_Address_Set(xsta, ysta, xend-1, yend-1); // 设置显示范围
    for(i = ysta; i < yend; i++)
    {													   	 	
        for(j = xsta; j < xend; j++)
        {
            LCD_WR_DATA(color);
        }
    } 					  	    
}

/**
 * @brief 在指定位置画一个点
 * @param x,y 画点坐标
 * @param color 点的颜色
 */
void LCD_DrawPoint(uint16_t x, uint16_t y, uint16_t color)
{
    LCD_Address_Set(x, y, x, y); // 设置光标位置 
    LCD_WR_DATA(color);
} 

/**
 * @brief 画线
 * @param x1,y1   起始坐标
 * @param x2,y2   终止坐标
 * @param color   线的颜色
 */
void LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    uint16_t t; 
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, uRow, uCol;
    delta_x = x2 - x1; // 计算坐标增量 
    delta_y = y2 - y1;
    uRow = x1; // 画线起点坐标
    uCol = y1;
    if(delta_x > 0)incx = 1; // 设置单步方向 
    else if (delta_x == 0)incx = 0; // 垂直线 
    else {incx = -1; delta_x = -delta_x;}
    if(delta_y > 0)incy = 1;
    else if (delta_y == 0)incy = 0; // 水平线 
    else {incy = -1; delta_y = -delta_y;}
    if(delta_x > delta_y)distance = delta_x; // 选取基本增量坐标轴 
    else distance = delta_y;
    for(t = 0; t < distance + 1; t++)
    {
        LCD_DrawPoint(uRow, uCol, color); // 画点
        xerr += delta_x;
        yerr += delta_y;
        if(xerr > distance)
        {
            xerr -= distance;
            uRow += incx;
        }
        if(yerr > distance)
        {
            yerr -= distance;
            uCol += incy;
        }
    }
}

/**
 * @brief 画矩形
 * @param x1,y1   起始坐标
 * @param x2,y2   终止坐标
 * @param color   矩形的颜色
 */
void LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    LCD_DrawLine(x1, y1, x2, y1, color);
    LCD_DrawLine(x1, y1, x1, y2, color);
    LCD_DrawLine(x1, y2, x2, y2, color);
    LCD_DrawLine(x2, y1, x2, y2, color);
}

/**
 * @brief 画圆
 * @param x0,y0   圆心坐标
 * @param r       半径
 * @param color   圆的颜色
 */
void Draw_Circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color)
{
    int a, b;
    int di;
    a = 0; b = r;	  
    di = 3 - (r << 1);       // 判断下个点位置的标志
    while(a <= b)
    {
        LCD_DrawPoint(x0 + a, y0 - b, color);  // 5
        LCD_DrawPoint(x0 + b, y0 - a, color);  // 0           
        LCD_DrawPoint(x0 + b, y0 + a, color);  // 4               
        LCD_DrawPoint(x0 + a, y0 + b, color);  // 6 
        LCD_DrawPoint(x0 - a, y0 + b, color);  // 1       
        LCD_DrawPoint(x0 - b, y0 + a, color);             
        LCD_DrawPoint(x0 - a, y0 - b, color);  // 2             
        LCD_DrawPoint(x0 - b, y0 - a, color);  // 7     	         
        a++;
        // 使用Bresenham算法画圆     
        if(di < 0)di += 4 * a + 6;	  
        else
        {
            di += 10 + 4 * (a - b);   
            b--;
        } 						    
    }
}

/**
 * @brief 幂函数
 * @param m 底数
 * @param n 指数
 * @return m^n次方
 */
uint32_t mypow(uint8_t m, uint8_t n)
{
    uint32_t result = 1;	 
    while(n--)result *= m;    
    return result;
}

/**
 * @brief 显示数字
 * @param x,y 起点坐标
 * @param num 要显示的数字
 * @param len 数字的位数
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 */
void LCD_ShowIntNum(uint16_t x, uint16_t y, uint16_t num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey)
{         	
    uint8_t t, temp;
    uint8_t enshow = 0;						   
    for(t = 0; t < len; t++)
    {
        temp = (num / mypow(10, len - t - 1)) % 10;
        if(enshow == 0 && t < (len - 1))
        {
            if(temp == 0)
            {
                LCD_ShowChar(x + (sizey / 2) * t, y, ' ', fc, bc, sizey, 0);
                continue;
            }else enshow = 1; 
        }
        LCD_ShowChar(x + (sizey / 2) * t, y, temp + '0', fc, bc, sizey, 0); 
    }
} 

/**
 * @brief 显示两位小数的浮点数
 * @param x,y 起点坐标
 * @param num 要显示的小数
 * @param len 数字的位数
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 */
void LCD_ShowFloatNum1(uint16_t x, uint16_t y, float num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey)
{
    uint8_t t, temp, sizex;
    uint16_t num1;
    sizex = sizey / 2;
    num1 = num * 100;
    for(t = 0; t < len; t++)
    {
        temp = (num1 / mypow(10, len - t - 1)) % 10;
        if(t == (len - 2))
        {
            LCD_ShowChar(x + (sizex) * t, y, '.', fc, bc, sizey, 0);
            t++;
            len += 1;
        }
        LCD_ShowChar(x + (sizex) * t, y, temp + '0', fc, bc, sizey, 0);
    }
}

/**
 * @brief 显示字符
 * @param x,y 起点坐标
 * @param num 要显示的字符
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowChar(uint16_t x, uint16_t y, uint8_t num, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    uint8_t sizex, t;
    uint16_t i;
    uint16_t x0 = x;
    sizex = sizey / 2;

    LCD_Address_Set(x, y, x + sizex - 1, y + sizey - 1); // 设置光标位置

    // 改进的字符显示 - 使用简单的点阵模式
    for(i = 0; i < sizey; i++)
    {
        for(t = 0; t < sizex; t++)
        {
            uint8_t show_pixel = 0;

            // 根据字符生成简单的点阵图案
            if(num >= '0' && num <= '9') {
                // 数字显示逻辑
                show_pixel = LCD_GetDigitPixel(num, t, i, sizex, sizey);
            }
            else if(num >= 'A' && num <= 'Z') {
                // 大写字母显示逻辑
                show_pixel = LCD_GetLetterPixel(num, t, i, sizex, sizey);
            }
            else if(num >= 'a' && num <= 'z') {
                // 小写字母显示逻辑
                show_pixel = LCD_GetLetterPixel(num - 32, t, i, sizex, sizey); // 转为大写
            }
            else if(num == ' ') {
                // 空格
                show_pixel = 0;
            }
            else {
                // 其他字符显示为边框
                show_pixel = ((i == 0) || (i == sizey-1) || (t == 0) || (t == sizex-1)) ? 1 : 0;
            }

            if(show_pixel) {
                LCD_WR_DATA(fc); // 字体颜色
            } else {
                LCD_WR_DATA(bc); // 背景颜色
            }
        }
    }
}

/**
 * @brief 显示字符串
 * @param x,y 起点坐标
 * @param p 字符串起始地址
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowString(uint16_t x, uint16_t y, const uint8_t *p, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    while(*p != '\0')
    {
        LCD_ShowChar(x, y, *p, fc, bc, sizey, mode);
        x += sizey / 2;
        p++;
    }
}

/**
 * @brief 显示中文字符 - 简化版本
 * @param x,y 起点坐标
 * @param s 要显示的中文字符串
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowChinese(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    // 简化的中文显示 - 显示一个方块代替
    LCD_Fill(x, y, x + sizey, y + sizey, fc);
}

// 其他中文显示函数的简化实现
void LCD_ShowChinese12x12(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 12, mode);
}

void LCD_ShowChinese16x16(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 16, mode);
}

void LCD_ShowChinese24x24(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 24, mode);
}

void LCD_ShowChinese32x32(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 32, mode);
}

/**
 * @brief 显示图片 - 简化版本
 * @param x,y 起点坐标
 * @param length 图片长度
 * @param width 图片宽度
 * @param pic 图片数组
 */
void LCD_ShowPicture(uint16_t x, uint16_t y, uint16_t length, uint16_t width, const uint8_t pic[])
{
    // 简化的图片显示 - 显示一个彩色矩形
    LCD_Fill(x, y, x + length, y + width, BLUE);
}
