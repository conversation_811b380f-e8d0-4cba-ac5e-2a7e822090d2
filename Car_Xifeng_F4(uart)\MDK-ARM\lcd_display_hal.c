#include "lcd_display_hal.h"

/**
 * @brief 获取数字字符的像素点
 * @param digit 数字字符 ('0'-'9')
 * @param x,y 像素坐标
 * @param w,h 字符宽度和高度
 * @return 1-显示像素，0-背景
 */
uint8_t LCD_GetDigitPixel(uint8_t digit, uint8_t x, uint8_t y, uint8_t w, uint8_t h)
{
    // 简化的数字点阵 - 使用相对坐标
    uint8_t cx = x * 5 / w;  // 转换为5x7点阵坐标
    uint8_t cy = y * 7 / h;

    // 5x7数字点阵数据 (简化版)
    const uint8_t digit_patterns[10][7] = {
        // '0'
        {0x0E, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // '1'
        {0x04, 0x0C, 0x04, 0x04, 0x04, 0x04, 0x0E},
        // '2'
        {0x0E, 0x11, 0x01, 0x02, 0x04, 0x08, 0x1F},
        // '3'
        {0x0E, 0x11, 0x01, 0x06, 0x01, 0x11, 0x0E},
        // '4'
        {0x02, 0x06, 0x0A, 0x12, 0x1F, 0x02, 0x02},
        // '5'
        {0x1F, 0x10, 0x1E, 0x01, 0x01, 0x11, 0x0E},
        // '6'
        {0x06, 0x08, 0x10, 0x1E, 0x11, 0x11, 0x0E},
        // '7'
        {0x1F, 0x01, 0x02, 0x04, 0x08, 0x08, 0x08},
        // '8'
        {0x0E, 0x11, 0x11, 0x0E, 0x11, 0x11, 0x0E},
        // '9'
        {0x0E, 0x11, 0x11, 0x0F, 0x01, 0x02, 0x0C}
    };

    if(digit >= '0' && digit <= '9' && cy < 7 && cx < 5) {
        uint8_t pattern = digit_patterns[digit - '0'][cy];
        return (pattern >> (4 - cx)) & 0x01;
    }
    return 0;
}

/**
 * @brief 获取字母字符的像素点
 * @param letter 字母字符 ('A'-'Z')
 * @param x,y 像素坐标
 * @param w,h 字符宽度和高度
 * @return 1-显示像素，0-背景
 */
uint8_t LCD_GetLetterPixel(uint8_t letter, uint8_t x, uint8_t y, uint8_t w, uint8_t h)
{
    // 简化的字母点阵 - 使用相对坐标
    uint8_t cx = x * 5 / w;  // 转换为5x7点阵坐标
    uint8_t cy = y * 7 / h;

    // 5x7字母点阵数据 (部分字母)
    const uint8_t letter_patterns[26][7] = {
        // 'A'
        {0x04, 0x0A, 0x11, 0x11, 0x1F, 0x11, 0x11},
        // 'B'
        {0x1E, 0x11, 0x11, 0x1E, 0x11, 0x11, 0x1E},
        // 'C'
        {0x0E, 0x11, 0x10, 0x10, 0x10, 0x11, 0x0E},
        // 'D'
        {0x1C, 0x12, 0x11, 0x11, 0x11, 0x12, 0x1C},
        // 'E'
        {0x1F, 0x10, 0x10, 0x1E, 0x10, 0x10, 0x1F},
        // 'F'
        {0x1F, 0x10, 0x10, 0x1E, 0x10, 0x10, 0x10},
        // 'G'
        {0x0E, 0x11, 0x10, 0x17, 0x11, 0x11, 0x0F},
        // 'H'
        {0x11, 0x11, 0x11, 0x1F, 0x11, 0x11, 0x11},
        // 'I'
        {0x0E, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0E},
        // 'J'
        {0x07, 0x02, 0x02, 0x02, 0x02, 0x12, 0x0C},
        // 'K'
        {0x11, 0x12, 0x14, 0x18, 0x14, 0x12, 0x11},
        // 'L'
        {0x10, 0x10, 0x10, 0x10, 0x10, 0x10, 0x1F},
        // 'M'
        {0x11, 0x1B, 0x15, 0x15, 0x11, 0x11, 0x11},
        // 'N'
        {0x11, 0x19, 0x15, 0x13, 0x11, 0x11, 0x11},
        // 'O'
        {0x0E, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // 'P'
        {0x1E, 0x11, 0x11, 0x1E, 0x10, 0x10, 0x10},
        // 'Q'
        {0x0E, 0x11, 0x11, 0x11, 0x15, 0x12, 0x0D},
        // 'R'
        {0x1E, 0x11, 0x11, 0x1E, 0x14, 0x12, 0x11},
        // 'S'
        {0x0F, 0x10, 0x10, 0x0E, 0x01, 0x01, 0x1E},
        // 'T'
        {0x1F, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04},
        // 'U'
        {0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0E},
        // 'V'
        {0x11, 0x11, 0x11, 0x11, 0x0A, 0x0A, 0x04},
        // 'W'
        {0x11, 0x11, 0x11, 0x15, 0x15, 0x1B, 0x11},
        // 'X'
        {0x11, 0x11, 0x0A, 0x04, 0x0A, 0x11, 0x11},
        // 'Y'
        {0x11, 0x11, 0x0A, 0x04, 0x04, 0x04, 0x04},
        // 'Z'
        {0x1F, 0x01, 0x02, 0x04, 0x08, 0x10, 0x1F}
    };

    if(letter >= 'A' && letter <= 'Z' && cy < 7 && cx < 5) {
        uint8_t pattern = letter_patterns[letter - 'A'][cy];
        return (pattern >> (4 - cx)) & 0x01;
    }
    return 0;
}

/**
 * @brief 在指定区域填充颜色
 * @param xsta,ysta   起始坐标
 * @param xend,yend   终止坐标
 * @param color       要填充的颜色
 */
void LCD_Fill(uint16_t xsta, uint16_t ysta, uint16_t xend, uint16_t yend, uint16_t color)
{          
    uint16_t i, j; 
    LCD_Address_Set(xsta, ysta, xend-1, yend-1); // 设置显示范围
    for(i = ysta; i < yend; i++)
    {													   	 	
        for(j = xsta; j < xend; j++)
        {
            LCD_WR_DATA(color);
        }
    } 					  	    
}

/**
 * @brief 在指定位置画一个点
 * @param x,y 画点坐标
 * @param color 点的颜色
 */
void LCD_DrawPoint(uint16_t x, uint16_t y, uint16_t color)
{
    LCD_Address_Set(x, y, x, y); // 设置光标位置 
    LCD_WR_DATA(color);
} 

/**
 * @brief 画线
 * @param x1,y1   起始坐标
 * @param x2,y2   终止坐标
 * @param color   线的颜色
 */
void LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    uint16_t t; 
    int xerr = 0, yerr = 0, delta_x, delta_y, distance;
    int incx, incy, uRow, uCol;
    delta_x = x2 - x1; // 计算坐标增量 
    delta_y = y2 - y1;
    uRow = x1; // 画线起点坐标
    uCol = y1;
    if(delta_x > 0)incx = 1; // 设置单步方向 
    else if (delta_x == 0)incx = 0; // 垂直线 
    else {incx = -1; delta_x = -delta_x;}
    if(delta_y > 0)incy = 1;
    else if (delta_y == 0)incy = 0; // 水平线 
    else {incy = -1; delta_y = -delta_y;}
    if(delta_x > delta_y)distance = delta_x; // 选取基本增量坐标轴 
    else distance = delta_y;
    for(t = 0; t < distance + 1; t++)
    {
        LCD_DrawPoint(uRow, uCol, color); // 画点
        xerr += delta_x;
        yerr += delta_y;
        if(xerr > distance)
        {
            xerr -= distance;
            uRow += incx;
        }
        if(yerr > distance)
        {
            yerr -= distance;
            uCol += incy;
        }
    }
}

/**
 * @brief 画矩形
 * @param x1,y1   起始坐标
 * @param x2,y2   终止坐标
 * @param color   矩形的颜色
 */
void LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color)
{
    LCD_DrawLine(x1, y1, x2, y1, color);
    LCD_DrawLine(x1, y1, x1, y2, color);
    LCD_DrawLine(x1, y2, x2, y2, color);
    LCD_DrawLine(x2, y1, x2, y2, color);
}

/**
 * @brief 画圆
 * @param x0,y0   圆心坐标
 * @param r       半径
 * @param color   圆的颜色
 */
void Draw_Circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color)
{
    int a, b;
    int di;
    a = 0; b = r;	  
    di = 3 - (r << 1);       // 判断下个点位置的标志
    while(a <= b)
    {
        LCD_DrawPoint(x0 + a, y0 - b, color);  // 5
        LCD_DrawPoint(x0 + b, y0 - a, color);  // 0           
        LCD_DrawPoint(x0 + b, y0 + a, color);  // 4               
        LCD_DrawPoint(x0 + a, y0 + b, color);  // 6 
        LCD_DrawPoint(x0 - a, y0 + b, color);  // 1       
        LCD_DrawPoint(x0 - b, y0 + a, color);             
        LCD_DrawPoint(x0 - a, y0 - b, color);  // 2             
        LCD_DrawPoint(x0 - b, y0 - a, color);  // 7     	         
        a++;
        // 使用Bresenham算法画圆     
        if(di < 0)di += 4 * a + 6;	  
        else
        {
            di += 10 + 4 * (a - b);   
            b--;
        } 						    
    }
}

/**
 * @brief 幂函数
 * @param m 底数
 * @param n 指数
 * @return m^n次方
 */
uint32_t mypow(uint8_t m, uint8_t n)
{
    uint32_t result = 1;	 
    while(n--)result *= m;    
    return result;
}

/**
 * @brief 显示数字
 * @param x,y 起点坐标
 * @param num 要显示的数字
 * @param len 数字的位数
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 */
void LCD_ShowIntNum(uint16_t x, uint16_t y, uint16_t num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey)
{         	
    uint8_t t, temp;
    uint8_t enshow = 0;						   
    for(t = 0; t < len; t++)
    {
        temp = (num / mypow(10, len - t - 1)) % 10;
        if(enshow == 0 && t < (len - 1))
        {
            if(temp == 0)
            {
                LCD_ShowChar(x + (sizey / 2) * t, y, ' ', fc, bc, sizey, 0);
                continue;
            }else enshow = 1; 
        }
        LCD_ShowChar(x + (sizey / 2) * t, y, temp + '0', fc, bc, sizey, 0); 
    }
} 

/**
 * @brief 显示两位小数的浮点数
 * @param x,y 起点坐标
 * @param num 要显示的小数
 * @param len 数字的位数
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 */
void LCD_ShowFloatNum1(uint16_t x, uint16_t y, float num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey)
{
    uint8_t t, temp, sizex;
    uint16_t num1;
    sizex = sizey / 2;
    num1 = num * 100;
    for(t = 0; t < len; t++)
    {
        temp = (num1 / mypow(10, len - t - 1)) % 10;
        if(t == (len - 2))
        {
            LCD_ShowChar(x + (sizex) * t, y, '.', fc, bc, sizey, 0);
            t++;
            len += 1;
        }
        LCD_ShowChar(x + (sizex) * t, y, temp + '0', fc, bc, sizey, 0);
    }
}

/**
 * @brief 显示字符
 * @param x,y 起点坐标
 * @param num 要显示的字符
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowChar(uint16_t x, uint16_t y, uint8_t num, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    uint8_t sizex, t;
    uint16_t i;
    uint16_t x0 = x;
    sizex = sizey / 2;

    LCD_Address_Set(x, y, x + sizex - 1, y + sizey - 1); // 设置光标位置

    // 改进的字符显示 - 使用简单的点阵模式
    for(i = 0; i < sizey; i++)
    {
        for(t = 0; t < sizex; t++)
        {
            uint8_t show_pixel = 0;

            // 根据字符生成简单的点阵图案
            if(num >= '0' && num <= '9') {
                // 数字显示逻辑
                show_pixel = LCD_GetDigitPixel(num, t, i, sizex, sizey);
            }
            else if(num >= 'A' && num <= 'Z') {
                // 大写字母显示逻辑
                show_pixel = LCD_GetLetterPixel(num, t, i, sizex, sizey);
            }
            else if(num >= 'a' && num <= 'z') {
                // 小写字母显示逻辑
                show_pixel = LCD_GetLetterPixel(num - 32, t, i, sizex, sizey); // 转为大写
            }
            else if(num == ' ') {
                // 空格
                show_pixel = 0;
            }
            else if(num == '.') {
                // 小数点 - 在右下角显示一个点
                show_pixel = ((i >= sizey-3) && (i < sizey-1) && (t >= sizex-3) && (t < sizex-1)) ? 1 : 0;
            }
            else if(num == ':') {
                // 冒号 - 显示两个点
                show_pixel = ((t == sizex/2) && ((i == sizey/3) || (i == sizey*2/3))) ? 1 : 0;
            }
            else if(num == '-') {
                // 减号/横线
                show_pixel = ((i == sizey/2) && (t >= 1) && (t < sizex-1)) ? 1 : 0;
            }
            else if(num == '/') {
                // 斜杠
                show_pixel = ((i + t) == (sizey + sizex)/2) ? 1 : 0;
            }
            else {
                // 其他字符显示为边框
                show_pixel = ((i == 0) || (i == sizey-1) || (t == 0) || (t == sizex-1)) ? 1 : 0;
            }

            if(show_pixel) {
                LCD_WR_DATA(fc); // 字体颜色
            } else {
                LCD_WR_DATA(bc); // 背景颜色
            }
        }
    }
}

/**
 * @brief 显示字符串
 * @param x,y 起点坐标
 * @param p 字符串起始地址
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowString(uint16_t x, uint16_t y, const uint8_t *p, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    while(*p != '\0')
    {
        LCD_ShowChar(x, y, *p, fc, bc, sizey, mode);
        x += sizey / 2;
        p++;
    }
}

/**
 * @brief 获取中文字符的简化显示模式
 */
uint8_t LCD_GetChinesePattern(const char* chinese_str, uint8_t x, uint8_t y, uint8_t w, uint8_t h)
{
    // 简化的中文字符识别和显示
    // 根据中文字符的前两个字节来识别常用字符

    if (strncmp(chinese_str, "公", 2) == 0) {
        // "公" - 显示一个简单的图案
        return ((x == w/2) || (y == h/3) || (y == h*2/3)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "里", 2) == 0) {
        // "里" - 显示方框
        return ((x == 0) || (x == w-1) || (y == 0) || (y == h-1)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "米", 2) == 0) {
        // "米" - 显示十字
        return ((x == w/2) || (y == h/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "分", 2) == 0) {
        // "分" - 显示分叉图案
        return ((y == h/2) || ((x == w/2) && (y > h/2))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "钟", 2) == 0) {
        // "钟" - 显示圆形
        int cx = w/2, cy = h/2;
        int dx = x - cx, dy = y - cy;
        return ((dx*dx + dy*dy) <= (w/3)*(w/3)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "从", 2) == 0) {
        // "从" - 显示双竖线
        return ((x == w/3) || (x == w*2/3)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "路", 2) == 0) {
        // "路" - 显示网格
        return ((x == w/2) || (y == h/3) || (y == h*2/3)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "出", 2) == 0) {
        // "出" - 显示箭头向上
        return ((x == w/2) || ((y == h*2/3) && (x >= w/3) && (x <= w*2/3))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "发", 2) == 0) {
        // "发" - 显示向上箭头
        return ((x == w/2) || ((y == h/4) && (x >= w/4) && (x <= w*3/4))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "左", 2) == 0) {
        // "左" - 显示向左箭头
        return ((y == h/2) || ((x == w/4) && (y >= h/3) && (y <= h*2/3))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "转", 2) == 0) {
        // "转" - 显示弯曲线
        return ((x + y == w) || (x == w/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "进", 2) == 0) {
        // "进" - 显示向前箭头
        return ((x == w/2) || ((y == h/2) && (x <= w*2/3))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "入", 2) == 0) {
        // "入" - 显示倒V形
        return ((x + y == w/2) || (x - y == w/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "继", 2) == 0) {
        // "继" - 显示连续线
        return ((y == h/3) || (y == h*2/3) || (x == w/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "续", 2) == 0) {
        // "续" - 显示波浪线
        return ((y == h/2 + (x % 3) - 1)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "沿", 2) == 0) {
        // "沿" - 显示平行线
        return ((y == h/3) || (y == h*2/3)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "直", 2) == 0) {
        // "直" - 显示直线
        return ((x == w/2) || (y == h/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "行", 2) == 0) {
        // "行" - 显示三横线
        return ((y == h/4) || (y == h/2) || (y == h*3/4)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "右", 2) == 0) {
        // "右" - 显示向右箭头
        return ((y == h/2) || ((x == w*3/4) && (y >= h/3) && (y <= h*2/3))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "到", 2) == 0) {
        // "到" - 显示目标点
        return ((x == w/2 && y == h/2) || ((x-w/2)*(x-w/2) + (y-h/2)*(y-h/2) == (w/4)*(w/4))) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "达", 2) == 0) {
        // "达" - 显示星形
        return ((x == w/2) || (y == h/2) || (x == y) || (x + y == w)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "目", 2) == 0) {
        // "目" - 显示方框加横线
        return ((x == 0) || (x == w-1) || (y == 0) || (y == h-1) || (y == h/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "的", 2) == 0) {
        // "的" - 显示圆点
        return ((x == w/2) && (y == h/2)) ? 1 : 0;
    }
    else if (strncmp(chinese_str, "地", 2) == 0) {
        // "地" - 显示方格
        return ((x % 2 == 0) && (y % 2 == 0)) ? 1 : 0;
    }

    // 默认显示一个小方块
    return ((x >= w/3) && (x <= w*2/3) && (y >= h/3) && (y <= h*2/3)) ? 1 : 0;
}

/**
 * @brief 显示中文字符 - 改进版本
 * @param x,y 起点坐标
 * @param s 要显示的中文字符串
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowChinese(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    uint16_t i, j;
    uint8_t sizex = sizey; // 中文字符是方形的

    LCD_Address_Set(x, y, x + sizex - 1, y + sizey - 1);

    for(i = 0; i < sizey; i++)
    {
        for(j = 0; j < sizex; j++)
        {
            uint8_t show_pixel = LCD_GetChinesePattern((const char*)s, j, i, sizex, sizey);

            if(show_pixel) {
                LCD_WR_DATA(fc); // 字体颜色
            } else {
                LCD_WR_DATA(bc); // 背景颜色
            }
        }
    }
}

// 其他中文显示函数的简化实现
void LCD_ShowChinese12x12(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 12, mode);
}

void LCD_ShowChinese16x16(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 16, mode);
}

void LCD_ShowChinese24x24(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 24, mode);
}

void LCD_ShowChinese32x32(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    LCD_ShowChinese(x, y, s, fc, bc, 32, mode);
}

/**
 * @brief 显示中英文混合字符串
 * @param x,y 起点坐标
 * @param str 要显示的字符串
 * @param fc 字体颜色
 * @param bc 背景颜色
 * @param sizey 字体大小
 * @param mode 叠加方式(1)还是非叠加方式(0)
 */
void LCD_ShowMixedString(uint16_t x, uint16_t y, const char *str, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode)
{
    uint16_t current_x = x;
    const char *p = str;

    while(*p != '\0')
    {
        if((*p & 0x80) == 0) {
            // ASCII字符
            LCD_ShowChar(current_x, y, *p, fc, bc, sizey, mode);
            current_x += sizey / 2;
            p++;
        }
        else {
            // 中文字符（UTF-8编码，通常占3个字节）
            // 简化处理：假设是2字节的中文字符
            LCD_ShowChinese(current_x, y, (uint8_t*)p, fc, bc, sizey, mode);
            current_x += sizey; // 中文字符宽度等于高度

            // 跳过中文字符的字节
            if((*p & 0xE0) == 0xC0) {
                p += 2; // 2字节UTF-8字符
            }
            else if((*p & 0xF0) == 0xE0) {
                p += 3; // 3字节UTF-8字符
            }
            else {
                p++; // 其他情况
            }
        }
    }
}

/**
 * @brief 显示图片 - 简化版本
 * @param x,y 起点坐标
 * @param length 图片长度
 * @param width 图片宽度
 * @param pic 图片数组
 */
void LCD_ShowPicture(uint16_t x, uint16_t y, uint16_t length, uint16_t width, const uint8_t pic[])
{
    // 简化的图片显示 - 显示一个彩色矩形
    LCD_Fill(x, y, x + length, y + width, BLUE);
}
