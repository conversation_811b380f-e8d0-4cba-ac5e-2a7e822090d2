<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Field3调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .debug-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .test-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .log-area {
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .value {
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 5px;
        }
        .success { color: #00ff00; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .info { color: #00bfff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Field3调试工具</h1>
        
        <div class="debug-section">
            <h3>🧪 测试不同的Field3值</h3>
            <button class="test-button" onclick="testField3(1111)">测试 1111 (万达)</button>
            <button class="test-button" onclick="testField3(2222)">测试 2222 (书院)</button>
            <button class="test-button" onclick="testField3(3333)">测试 3333 (体育)</button>
            <button class="test-button" onclick="testField3(4444)">测试 4444 (火车)</button>
            <button class="test-button" onclick="testField3(5555)">测试 5555 (医院)</button>
            <button class="test-button" onclick="testField3(9999)">测试 9999 (万达兼容)</button>
        </div>

        <div class="status-grid">
            <div class="status-item">
                <div>最后测试的Field3</div>
                <div class="value" id="lastField3">-</div>
            </div>
            <div class="status-item">
                <div>检测结果</div>
                <div class="value" id="detectionResult">-</div>
            </div>
        </div>

        <div class="debug-section">
            <h3>📋 调试日志</h3>
            <div class="log-area" id="debugLog"></div>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>

        <div class="debug-section">
            <h3>🔍 问题诊断</h3>
            <p><strong>可能的问题原因：</strong></p>
            <ul>
                <li><strong>Entry ID重复</strong>：ThingSpeak可能返回相同的entry_id</li>
                <li><strong>数据类型问题</strong>：field3可能不是整数类型</li>
                <li><strong>网络延迟</strong>：数据可能还没有到达ThingSpeak</li>
                <li><strong>API限制</strong>：ThingSpeak有15秒的更新间隔限制</li>
            </ul>
            
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>等待至少15秒再发送下一个命令</li>
                <li>检查单片机是否真的发送了field3=5555</li>
                <li>查看ThingSpeak频道确认数据是否上传成功</li>
                <li>重新启动检测器清除Entry ID缓存</li>
            </ul>
        </div>
    </div>

    <script>
        // 复制检测器中的关键函数
        const DESTINATIONS = {
            '1': { name: '万达广场(衡阳酃湖店)', icon: '🛍️' },
            '2': { name: '酃湖书院', icon: '📚' },
            '3': { name: '衡阳市体育中心', icon: '🏟️' },
            '4': { name: '衡阳火车站', icon: '🚄' },
            '5': { name: '南华大学附属第一医院', icon: '🏥' },
            'wanda': { name: '万达广场(兼容模式)', icon: '🛍️' }
        };

        function getDestinationKey(field3) {
            switch (field3) {
                case 1111: return '1';
                case 2222: return '2';
                case 3333: return '3';
                case 4444: return '4';
                case 5555: return '5';
                case 9999: return 'wanda';
                default: return null;
            }
        }

        function log(message, type = 'info') {
            const logArea = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: '#00bfff',
                success: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${time}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function testField3(field3Value) {
            document.getElementById('lastField3').textContent = field3Value;
            
            log(`🧪 测试 field3=${field3Value}`, 'info');
            log(`🔍 field3类型: ${typeof field3Value}`, 'info');
            
            const destinationKey = getDestinationKey(field3Value);
            log(`🔍 getDestinationKey(${field3Value}) = ${destinationKey}`, 'info');
            
            if (destinationKey) {
                const destination = DESTINATIONS[destinationKey];
                log(`✅ 找到目的地: ${destination.icon} ${destination.name}`, 'success');
                document.getElementById('detectionResult').textContent = `${destination.icon} ${destination.name}`;
                document.getElementById('detectionResult').className = 'value success';
            } else {
                log(`❌ 未找到对应的目的地`, 'error');
                document.getElementById('detectionResult').textContent = '未识别';
                document.getElementById('detectionResult').className = 'value error';
            }
            
            log(`---`, 'info');
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
            log('🧹 日志已清除', 'info');
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🔧 Field3调试工具已启动', 'success');
            log('💡 点击按钮测试不同的field3值', 'info');
        };
    </script>
</body>
</html>
