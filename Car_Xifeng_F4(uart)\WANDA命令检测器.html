<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚗 智能导航命令检测器 & 路径规划系统</title>

    <!-- Leaflet CSS for map -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status-card {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            font-size: 1.1em;
        }
        .status-value {
            font-weight: bold;
            padding: 8px 15px;
            border-radius: 20px;
            background: rgba(0,0,0,0.3);
        }
        .success { color: #00ff88; background: rgba(0,255,136,0.2) !important; }
        .waiting { color: #ffaa00; background: rgba(255,170,0,0.2) !important; }
        .error { color: #ff4444; background: rgba(255,68,68,0.2) !important; }
        .btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .log-container {
            background: rgba(0,0,0,0.5);
            padding: 20px;
            border-radius: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border: 1px solid rgba(255,255,255,0.2);
            margin-top: 20px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .instruction {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #00ff88;
        }

        /* 地图样式 */
        #map {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            margin: 20px 0;
            border: 2px solid rgba(255,255,255,0.3);
            display: none; /* 初始隐藏 */
        }

        .navigation-panel {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
            display: none; /* 初始隐藏 */
        }

        .route-info {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            padding: 10px;
            background: rgba(0,0,0,0.3);
            border-radius: 10px;
        }

        .route-info-item {
            text-align: center;
        }

        .route-info-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #00ff88;
        }

        .route-info-label {
            font-size: 0.9em;
            color: #ccc;
            margin-top: 5px;
        }

        /* 导航指令样式 */
        .instructions-panel {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
            display: none; /* 初始隐藏 */
            max-height: 400px;
            overflow-y: auto;
        }

        .instruction-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }

        .instruction-number {
            background: #00ff88;
            color: #000;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .instruction-content {
            flex: 1;
        }

        .instruction-text {
            font-size: 1.1em;
            margin-bottom: 5px;
        }

        .instruction-distance {
            font-size: 0.9em;
            color: #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 智能导航命令检测器</h1>

        <div class="instruction">
            <h3>📋 使用说明</h3>
            <p>1. 点击"开始检测"按钮</p>
            <p>2. 在串口调试助手中发送导航命令</p>
            <p>3. 观察下方状态变化和导航启动</p>

            <h4>🎯 支持的导航命令</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-top: 10px; font-size: 0.9em;">
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=1111</strong> → 🛍️ 万达广场
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=2222</strong> → 📚 酃湖书院
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=3333</strong> → 🏟️ 体育中心
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=4444</strong> → 🚄 火车站
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=5555</strong> → 🏥 医院
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=6666</strong> → 🎓 衡阳师范学院
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 6px; border-radius: 6px;">
                    <strong>field3=9999</strong> → 🛍️ 万达(兼容)
                </div>
            </div>
        </div>

        <div class="status-card">
            <h3>📊 检测状态</h3>
            <div class="status-item">
                <span>📡 ThingSpeak连接:</span>
                <span id="connectionStatus" class="status-value waiting">检查中...</span>
            </div>
            <div class="status-item">
                <span>🎯 WANDA命令:</span>
                <span id="wandaStatus" class="status-value waiting">等待中...</span>
            </div>
            <div class="status-item">
                <span>📍 最新数据:</span>
                <span id="latestData" class="status-value waiting">无</span>
            </div>
            <div class="status-item">
                <span>⏰ 检测时间:</span>
                <span id="detectionTime" class="status-value waiting">--</span>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="startDetection()">🚀 开始检测</button>
            <button class="btn" onclick="stopDetection()">⏹️ 停止检测</button>
            <button class="btn" onclick="resetDetector()">🔄 重置检测器</button>
            <button class="btn" onclick="testConnection()">🔧 测试连接</button>
            <button class="btn" onclick="clearLogs()">🧹 清除日志</button>
            <button class="btn" onclick="toggleMap()" id="mapToggleBtn" style="display:none;">🗺️ 显示地图</button>
            <button class="btn" onclick="toggleInstructions()" id="instructionsToggleBtn" style="display:none;">📋 显示导航指令</button>
        </div>

        <!-- 导航面板 -->
        <div class="navigation-panel" id="navigationPanel">
            <h3>🧭 导航信息</h3>
            <div class="route-info">
                <div class="route-info-item">
                    <div class="route-info-value" id="routeDistance">--</div>
                    <div class="route-info-label">距离</div>
                </div>
                <div class="route-info-item">
                    <div class="route-info-value" id="routeTime">--</div>
                    <div class="route-info-label">预计时间</div>
                </div>
                <div class="route-info-item">
                    <div class="route-info-value" id="routeProvider">--</div>
                    <div class="route-info-label">路径规划</div>
                </div>
            </div>
        </div>

        <!-- 导航指令面板 -->
        <div class="instructions-panel" id="instructionsPanel">
            <h3>📋 导航指令</h3>
            <div id="instructionsList">
                <!-- 动态生成导航指令 -->
            </div>
        </div>

        <!-- 地图容器 -->
        <div id="map"></div>

        <div class="log-container" id="logContainer">
            <div style="color: #00ff00;">[系统] WANDA命令检测器已启动</div>
            <div style="color: #ffaa00;">[提示] 请点击"开始检测"，然后发送wanda命令</div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <script>
        // ThingSpeak配置
        const CONFIG = {
            CHANNEL_ID: '3014831',
            READ_API_KEY: 'V64RR7CZJ9Z4O7ED',
            BASE_URL: 'https://api.thingspeak.com'
        };

        let detectionInterval;
        let isDetecting = false;
        let lastEntryId = null;

        // 导航相关变量
        let map;
        let currentMarker;
        let routeLayer;
        let isMapVisible = false;
        let isInstructionsVisible = false;
        let currentRouteSteps = [];

        // 预设目的地选项（从导航文件中提取）
        const DESTINATIONS = {
            '1': {
                lat: 26.8892785,
                lon: 112.6609182,
                name: '万达广场(衡阳酃湖店)',
                icon: '🛍️',
                description: '万达广场（衡阳酃湖店）购物中心'
            },
            '2': {
                lat: 26.8850,
                lon: 112.6700,
                name: '酃湖书院',
                icon: '📚',
                description: '酃湖书院文化景点'
            },
            '3': {
                lat: 26.8900796,
                lon: 112.6741752,
                name: '衡阳市体育中心',
                icon: '🏟️',
                description: '衡阳市体育中心体育场馆'
            },
            '4': {
                lat: 26.8934986,
                lon: 112.6260051,
                name: '衡阳火车站',
                icon: '🚄',
                description: '衡阳火车站交通枢纽'
            },
            '5': {
                lat: 26.9043654,
                lon: 112.5962734,
                name: '南华大学附属第一医院',
                icon: '🏥',
                description: '南华大学附属第一医院医疗中心'
            },
            '6': {
                lat: 26.8812,
                lon: 112.6769,
                name: '衡阳师范学院',
                icon: '🎓',
                description: '衡阳师范学院教育学府'
            },
            // 保持向后兼容
            'wanda': {
                lat: 26.8892785,
                lon: 112.6609182,
                name: '万达广场(衡阳酃湖店)',
                icon: '🛍️',
                description: '万达广场（衡阳酃湖店）购物中心'
            }
        };

        // 湖南工学院默认坐标
        const DEFAULT_LAT = 26.8848140;
        const DEFAULT_LON = 112.6796025;

        // 根据field3值获取目的地键
        function getDestinationKey(field3) {
            switch (field3) {
                case 1111: return '1';  // 万达广场
                case 2222: return '2';  // 酃湖书院
                case 3333: return '3';  // 体育中心
                case 4444: return '4';  // 火车站
                case 5555: return '5';  // 医院
                case 6666: return '6';  // 衡阳师范学院
                case 9999: return 'wanda'; // 向后兼容
                default: return null;
            }
        }

        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: '#00bfff',
                success: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00'
            };

            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态
        function updateStatus(elementId, value, className = 'waiting') {
            const element = document.getElementById(elementId);
            element.textContent = value;
            element.className = `status-value ${className}`;
            if (className === 'success') {
                element.classList.add('pulse');
                setTimeout(() => element.classList.remove('pulse'), 3000);
            }
        }

        // 检测WANDA命令
        async function checkForWandaCommand() {
            try {
                const url = `${CONFIG.BASE_URL}/channels/${CONFIG.CHANNEL_ID}/feeds/last.json?api_key=${CONFIG.READ_API_KEY}`;
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                if (!data || !data.created_at) {
                    updateStatus('connectionStatus', '❌ 无数据', 'error');
                    return;
                }

                updateStatus('connectionStatus', '✅ 连接正常', 'success');
                updateStatus('detectionTime', new Date(data.created_at).toLocaleString(), 'success');

                // 检查WANDA命令标识
                // field3 = 9999 表示WANDA命令
                const lat = parseFloat(data.field1);
                const lon = parseFloat(data.field2);
                const field3 = parseFloat(data.field3);

                // 调试信息
                log(`🔍 调试信息: field3=${field3} (类型: ${typeof field3})`, 'info');

                // 检测导航命令
                const destinationKey = getDestinationKey(field3);
                log(`🔍 destinationKey=${destinationKey}`, 'info');

                if (destinationKey) {
                    const destination = DESTINATIONS[destinationKey];
                    const commandData = `NAV_${destinationKey}_${lat.toFixed(6)}_${lon.toFixed(6)}`;

                    updateStatus('wandaStatus', `✅ 导航命令已检测到: ${destination.icon} ${destination.name}`, 'success');
                    updateStatus('latestData', commandData, 'success');
                    log(`🎉 检测到导航命令: ${commandData}`, 'success');
                    log(`📊 Entry ID: ${data.entry_id}`, 'success');

                    // 检查是否是新的命令
                    log(`🔍 Entry ID检查: 当前=${data.entry_id}, 上次=${lastEntryId}`, 'info');
                    if (lastEntryId !== data.entry_id) {
                        lastEntryId = data.entry_id;
                        log(`🆕 这是新的导航命令！`, 'success');

                        // 显示GPS坐标信息
                        log(`📍 当前位置: ${lat.toFixed(6)}°N, ${lon.toFixed(6)}°E`, 'info');
                        log(`🎯 目标: ${destination.name} (${destination.lat.toFixed(4)}°N, ${destination.lon.toFixed(4)}°E)`, 'info');
                        log(`🏷️ 标识符: field3=${field3} (${destination.icon} ${destination.name})`, 'info');

                        // 启动导航功能
                        startNavigation(lat, lon, destinationKey);
                    } else {
                        log(`⚠️ Entry ID重复，跳过导航启动`, 'warning');
                    }
                } else {
                    updateStatus('wandaStatus', '⏳ 等待导航命令 (1-5或WANDA)', 'waiting');
                    updateStatus('latestData', `普通GPS: ${lat.toFixed(6)}°N, ${lon.toFixed(6)}°E, 海拔=${field3}m`, 'waiting');
                    log(`📍 检测到普通GPS数据: ${lat.toFixed(6)}°N, ${lon.toFixed(6)}°E, 海拔=${field3}m`, 'info');
                }

            } catch (error) {
                updateStatus('connectionStatus', '❌ 连接失败', 'error');
                log(`❌ 检测失败: ${error.message}`, 'error');
            }
        }

        // 开始检测
        function startDetection() {
            if (isDetecting) return;

            isDetecting = true;
            log('🚀 开始检测WANDA命令...', 'info');
            log('💡 请在串口调试助手中发送 "wanda" 命令', 'warning');

            // 立即检测一次
            checkForWandaCommand();

            // 每5秒检测一次
            detectionInterval = setInterval(checkForWandaCommand, 5000);
        }

        // 停止检测
        function stopDetection() {
            if (!isDetecting) return;

            isDetecting = false;
            clearInterval(detectionInterval);
            log('⏹️ 已停止检测', 'warning');
        }

        // 重置检测器
        function resetDetector() {
            // 停止当前检测
            stopDetection();

            // 清除Entry ID缓存
            lastEntryId = null;

            // 重置状态显示
            updateStatus('wandaStatus', '⏳ 等待导航命令 (1-5或WANDA)', 'waiting');
            updateStatus('latestData', '--', 'waiting');
            updateStatus('connectionStatus', '检查中...', 'waiting');
            updateStatus('detectionTime', '--', 'waiting');

            log('🔄 检测器已重置，Entry ID缓存已清除', 'success');
            log('💡 现在可以重新开始检测', 'info');
        }

        // 测试连接
        async function testConnection() {
            log('🔧 测试ThingSpeak连接...', 'info');
            await checkForWandaCommand();
        }

        // 清除日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('🧹 日志已清除', 'info');
        }

        // 初始化地图
        function initMap() {
            if (map) return; // 避免重复初始化

            log('🗺️ 初始化地图...', 'info');

            map = L.map('map').setView([DEFAULT_LAT, DEFAULT_LON], 15);

            // 添加地图图层
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19
            }).addTo(map);

            log('✅ 地图初始化完成', 'success');
        }

        // 启动导航功能
        async function startNavigation(currentLat, currentLon, destinationKey = 'wanda') {
            const destination = DESTINATIONS[destinationKey];
            if (!destination) {
                log(`❌ 未找到目的地: ${destinationKey}`, 'error');
                return;
            }

            log(`🧭 启动导航功能... 目的地: ${destination.icon} ${destination.name}`, 'info');

            // 初始化地图
            initMap();

            // 显示导航面板和按钮
            document.getElementById('navigationPanel').style.display = 'block';
            document.getElementById('mapToggleBtn').style.display = 'inline-block';
            document.getElementById('instructionsToggleBtn').style.display = 'inline-block';

            // 添加当前位置标记
            if (currentMarker) {
                map.removeLayer(currentMarker);
            }
            currentMarker = L.marker([currentLat, currentLon])
                .addTo(map)
                .bindPopup('📍 当前位置')
                .openPopup();

            // 添加目的地标记
            const destinationMarker = L.marker([destination.lat, destination.lon])
                .addTo(map)
                .bindPopup(`${destination.icon} ${destination.name}`);

            // 规划路线
            await planRoute(currentLat, currentLon, destination.lat, destination.lon, destination.name);

            log(`✅ 导航已启动！目的地: ${destination.name}`, 'success');
        }

        // 路径规划
        async function planRoute(startLat, startLon, endLat, endLon, destinationName = '目的地') {
            try {
                log('🗺️ 正在规划路线...', 'info');

                // 使用OSRM进行路径规划
                const url = `https://router.project-osrm.org/route/v1/driving/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true`;

                const response = await fetch(url);
                const data = await response.json();

                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];

                    // 显示路线信息
                    const distance = (route.distance / 1000).toFixed(2);
                    const duration = Math.round(route.duration / 60);

                    document.getElementById('routeDistance').textContent = `${distance} km`;
                    document.getElementById('routeTime').textContent = `${duration} 分钟`;
                    document.getElementById('routeProvider').textContent = 'OSRM';

                    // 保存导航步骤
                    if (route.legs && route.legs[0] && route.legs[0].steps) {
                        currentRouteSteps = route.legs[0].steps;
                        displayNavigationInstructions(currentRouteSteps);
                    }

                    // 在地图上显示路线
                    displayRoute(route.geometry.coordinates);

                    log(`✅ 路线规划成功: ${distance}km, ${duration}分钟`, 'success');
                    log(`📋 导航指令已生成，共${currentRouteSteps.length}步`, 'success');
                } else {
                    throw new Error('路径规划失败');
                }
            } catch (error) {
                log(`❌ 路径规划失败: ${error.message}`, 'error');
                // 显示直线距离作为备选
                showDirectRoute(startLat, startLon, endLat, endLon);
            }
        }

        // 在地图上显示路线
        function displayRoute(coordinates) {
            // 清除之前的路线
            if (routeLayer) {
                map.removeLayer(routeLayer);
            }

            // 转换坐标格式 (OSRM返回[lon,lat]，Leaflet需要[lat,lon])
            const latLngs = coordinates.map(coord => [coord[1], coord[0]]);

            // 创建路线
            routeLayer = L.polyline(latLngs, {
                color: '#FF4444',
                weight: 5,
                opacity: 0.8
            }).addTo(map);

            // 调整地图视图以显示整条路线
            map.fitBounds(routeLayer.getBounds(), { padding: [20, 20] });
        }

        // 显示直线路线（备选方案）
        function showDirectRoute(startLat, startLon, endLat, endLon) {
            log('📏 显示直线路线...', 'info');

            // 计算直线距离
            const distance = calculateDistance(startLat, startLon, endLat, endLon);
            const duration = Math.round(distance / 60 * 60); // 假设60km/h

            document.getElementById('routeDistance').textContent = `${distance.toFixed(2)} km`;
            document.getElementById('routeTime').textContent = `${duration} 分钟`;
            document.getElementById('routeProvider').textContent = '直线距离';

            // 显示直线
            if (routeLayer) {
                map.removeLayer(routeLayer);
            }

            routeLayer = L.polyline([
                [startLat, startLon],
                [endLat, endLon]
            ], {
                color: '#FFA500',
                weight: 3,
                opacity: 0.8,
                dashArray: '10, 10'
            }).addTo(map);

            map.fitBounds(routeLayer.getBounds(), { padding: [50, 50] });
        }

        // 计算两点间距离
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 显示导航指令
        function displayNavigationInstructions(steps) {
            const instructionsList = document.getElementById('instructionsList');
            instructionsList.innerHTML = '';

            steps.forEach((step, index) => {
                const instruction = translateOSRMInstruction(step);
                const distance = step.distance;

                const instructionItem = document.createElement('div');
                instructionItem.className = 'instruction-item';

                instructionItem.innerHTML = `
                    <div class="instruction-number">${index + 1}</div>
                    <div class="instruction-content">
                        <div class="instruction-text">${instruction}</div>
                        <div class="instruction-distance">${formatDistance(distance)}</div>
                    </div>
                `;

                instructionsList.appendChild(instructionItem);
            });

            log(`📋 已生成${steps.length}条导航指令`, 'success');
        }

        // 翻译OSRM导航指令为中文
        function translateOSRMInstruction(step) {
            const maneuver = step.maneuver;
            const roadName = step.name || '道路';

            let instruction = '';

            switch (maneuver.type) {
                case 'depart':
                    instruction = `从${roadName}出发`;
                    break;
                case 'arrive':
                    instruction = `到达目的地`;
                    break;
                case 'turn':
                    const direction = maneuver.modifier;
                    if (direction === 'left') instruction = `左转进入${roadName}`;
                    else if (direction === 'right') instruction = `右转进入${roadName}`;
                    else if (direction === 'sharp left') instruction = `急左转进入${roadName}`;
                    else if (direction === 'sharp right') instruction = `急右转进入${roadName}`;
                    else if (direction === 'slight left') instruction = `稍向左转进入${roadName}`;
                    else if (direction === 'slight right') instruction = `稍向右转进入${roadName}`;
                    else instruction = `转弯进入${roadName}`;
                    break;
                case 'continue':
                    instruction = `继续沿${roadName}直行`;
                    break;
                case 'merge':
                    instruction = `并入${roadName}`;
                    break;
                case 'roundabout':
                    instruction = `进入环岛，第${maneuver.exit || 1}个出口驶出到${roadName}`;
                    break;
                case 'new name':
                    instruction = `继续前行，道路变为${roadName}`;
                    break;
                default:
                    instruction = `沿${roadName}行驶`;
            }

            return instruction;
        }

        // 格式化距离显示
        function formatDistance(distance) {
            if (distance >= 1000) {
                return `${(distance/1000).toFixed(1)}公里`;
            } else {
                return `${Math.round(distance)}米`;
            }
        }

        // 切换导航指令显示
        function toggleInstructions() {
            const instructionsElement = document.getElementById('instructionsPanel');
            const toggleBtn = document.getElementById('instructionsToggleBtn');

            if (isInstructionsVisible) {
                instructionsElement.style.display = 'none';
                toggleBtn.textContent = '📋 显示导航指令';
                isInstructionsVisible = false;
            } else {
                instructionsElement.style.display = 'block';
                toggleBtn.textContent = '📋 隐藏导航指令';
                isInstructionsVisible = true;
            }
        }

        // 切换地图显示
        function toggleMap() {
            const mapElement = document.getElementById('map');
            const toggleBtn = document.getElementById('mapToggleBtn');

            if (isMapVisible) {
                mapElement.style.display = 'none';
                toggleBtn.textContent = '🗺️ 显示地图';
                isMapVisible = false;
            } else {
                mapElement.style.display = 'block';
                toggleBtn.textContent = '🗺️ 隐藏地图';
                isMapVisible = true;

                // 刷新地图大小
                if (map) {
                    setTimeout(() => map.invalidateSize(), 100);
                }
            }
        }

        // 页面加载完成
        window.onload = function() {
            log('🚗 智能导航命令检测器已就绪', 'success');
            log('📋 支持的导航命令:', 'info');
            log('  • field3=1111 → 🛍️ 万达广场', 'info');
            log('  • field3=2222 → 📚 酃湖书院', 'info');
            log('  • field3=3333 → 🏟️ 体育中心', 'info');
            log('  • field3=4444 → 🚄 火车站', 'info');
            log('  • field3=5555 → 🏥 医院', 'info');
            log('  • field3=6666 → 🎓 衡阳师范学院', 'info');
            log('  • field3=9999 → 🛍️ 万达(兼容)', 'info');
            log('🚀 点击"开始检测"开始使用', 'success');
        };
    </script>
</body>
</html>
