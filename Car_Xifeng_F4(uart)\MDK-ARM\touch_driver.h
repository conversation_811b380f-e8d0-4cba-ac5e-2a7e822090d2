/**
 * 电容触摸屏驱动头文件
 * 支持常见的电容触摸IC：FT6236、GT911、CST816等
 */

#ifndef __TOUCH_DRIVER_H__
#define __TOUCH_DRIVER_H__

#include "MyDefine.h"

// 触摸点结构体
typedef struct {
    uint16_t x;          // X坐标
    uint16_t y;          // Y坐标
    uint8_t valid;       // 触摸点是否有效
    uint8_t pressure;    // 压力值（0-255）
} TouchPoint_t;

// 触摸事件类型
typedef enum {
    TOUCH_EVENT_NONE = 0,
    TOUCH_EVENT_PRESS,      // 按下
    TOUCH_EVENT_RELEASE,    // 释放
    TOUCH_EVENT_MOVE,       // 移动
    TOUCH_EVENT_SWIPE_UP,   // 向上滑动
    TOUCH_EVENT_SWIPE_DOWN, // 向下滑动
    TOUCH_EVENT_SWIPE_LEFT, // 向左滑动
    TOUCH_EVENT_SWIPE_RIGHT // 向右滑动
} TouchEvent_t;

// 触摸状态结构体
typedef struct {
    TouchPoint_t current;     // 当前触摸点
    TouchPoint_t last;        // 上一次触摸点
    TouchEvent_t event;       // 触摸事件
    uint32_t press_time;      // 按下时间
    uint32_t release_time;    // 释放时间
    uint8_t gesture_detected; // 手势检测标志
} TouchState_t;

// 触摸区域定义
typedef struct {
    uint16_t x1, y1;     // 左上角
    uint16_t x2, y2;     // 右下角
    uint8_t id;          // 区域ID
    const char* name;    // 区域名称
} TouchArea_t;

// 常用触摸区域ID
#define TOUCH_AREA_NONE        0
#define TOUCH_AREA_PREV_PAGE   1  // 上一页
#define TOUCH_AREA_NEXT_PAGE   2  // 下一页
#define TOUCH_AREA_BACK        3  // 返回
#define TOUCH_AREA_HOME        4  // 主页
#define TOUCH_AREA_NAV_STEP    5  // 导航步骤区域

// 触摸配置
#define TOUCH_I2C_ADDR         0x38  // FT6236默认地址
#define TOUCH_MAX_POINTS       2     // 最大触摸点数
#define TOUCH_DEBOUNCE_TIME    50    // 消抖时间(ms)
#define TOUCH_SWIPE_THRESHOLD  30    // 滑动阈值(像素)
#define TOUCH_LONG_PRESS_TIME  1000  // 长按时间(ms)

// 函数声明
void Touch_Init(void);
uint8_t Touch_Scan(TouchState_t* touch_state);
TouchEvent_t Touch_GetEvent(void);
uint8_t Touch_IsPressed(void);
uint8_t Touch_GetArea(uint16_t x, uint16_t y);
void Touch_RegisterArea(uint8_t id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const char* name);
void Touch_ClearAreas(void);
uint8_t Touch_DetectGesture(TouchState_t* touch_state);

// 触摸校准函数
void Touch_Calibrate(void);
void Touch_SetCalibration(uint16_t x_min, uint16_t x_max, uint16_t y_min, uint16_t y_max);

#endif /* __TOUCH_DRIVER_H__ */
