/**
 * 电容触摸屏驱动头文件
 * 支持常见的电容触摸IC：FT6236、GT911、CST816等
 */

#ifndef __TOUCH_DRIVER_H__
#define __TOUCH_DRIVER_H__

#include "navigation_types.h"

// 函数声明
void Touch_Init(void);
uint8_t Touch_Scan(TouchState_t* touch_state);
TouchEvent_t Touch_GetEvent(void);
uint8_t Touch_IsPressed(void);
uint8_t Touch_GetArea(uint16_t x, uint16_t y);
void Touch_RegisterArea(uint8_t id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const char* name);
void Touch_ClearAreas(void);
uint8_t Touch_DetectGesture(TouchState_t* touch_state);

// 触摸校准函数
void Touch_Calibrate(void);
void Touch_SetCalibration(uint16_t x_min, uint16_t x_max, uint16_t y_min, uint16_t y_max);

#endif /* __TOUCH_DRIVER_H__ */
