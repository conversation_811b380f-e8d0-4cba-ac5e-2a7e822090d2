<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<ProjectGui xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_guix.xsd">

  <SchemaVersion>-6.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <PrjGuiSettings>
    <LastAddFilePath>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM</LastAddFilePath>
  </PrjGuiSettings>

  <ViewPool/>

  <SECTreeCtrl>
    <View>
      <WinId>38003</WinId>
      <ViewName>Registers</ViewName>
      <TableColWidths>115 67</TableColWidths>
    </View>
    <View>
      <WinId>346</WinId>
      <ViewName>Code Coverage</ViewName>
      <TableColWidths>770 160</TableColWidths>
    </View>
    <View>
      <WinId>204</WinId>
      <ViewName>Performance Analyzer</ViewName>
      <TableColWidths>930</TableColWidths>
    </View>
  </SECTreeCtrl>

  <TreeListPane>
    <View>
      <WinId>35141</WinId>
      <ViewName>Event Statistics</ViewName>
      <UserString></UserString>
      <TableColWidths>200 50 700</TableColWidths>
    </View>
    <View>
      <WinId>1506</WinId>
      <ViewName>Symbols</ViewName>
      <UserString></UserString>
      <TableColWidths>64 64 64</TableColWidths>
    </View>
    <View>
      <WinId>1936</WinId>
      <ViewName>Watch 1</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>1937</WinId>
      <ViewName>Watch 2</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>1935</WinId>
      <ViewName>Call Stack + Locals</ViewName>
      <UserString></UserString>
      <TableColWidths>200 133 133</TableColWidths>
    </View>
    <View>
      <WinId>2506</WinId>
      <ViewName>Trace Data</ViewName>
      <UserString></UserString>
      <TableColWidths>75 135 130 95 70 230 200 150</TableColWidths>
    </View>
    <View>
      <WinId>466</WinId>
      <ViewName>Source Browser</ViewName>
      <UserString>500</UserString>
      <TableColWidths>300</TableColWidths>
    </View>
  </TreeListPane>

  <CompViewPool/>

  <WindowSettings>
    <LogicAnalizer>
      <ShowLACursor>1</ShowLACursor>
      <ShowSignalInfo>1</ShowSignalInfo>
      <ShowCycles>0</ShowCycles>
      <LeftSideBarSize>0</LeftSideBarSize>
      <TimeBaseIndex>-1</TimeBaseIndex>
    </LogicAnalizer>
  </WindowSettings>

  <WinLayoutEx>
    <sActiveDebugView></sActiveDebugView>
    <WindowPosition>
      <length>44</length>
      <flags>2</flags>
      <showCmd>3</showCmd>
      <MinPosition>
        <xPos>-32000</xPos>
        <yPos>-32000</yPos>
      </MinPosition>
      <MaxPosition>
        <xPos>-1</xPos>
        <yPos>-1</yPos>
      </MaxPosition>
      <NormalPosition>
        <Top>188</Top>
        <Left>161</Left>
        <Right>946</Right>
        <Bottom>731</Bottom>
      </NormalPosition>
    </WindowPosition>
    <MDIClientArea>
      <RegID>0</RegID>
      <MDITabState>
        <Len>7412</Len>
        <Data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ata>
      </MDITabState>
    </MDIClientArea>
    <ViewEx>
      <ViewType>0</ViewType>
      <ViewName>Build</ViewName>
      <Window>
        <RegID>-1</RegID>
        <PaneID>-1</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C40000004F00000070040000BC000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1005</RegID>
        <PaneID>1005</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0300000066000000E00000007C020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>109</RegID>
        <PaneID>109</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0300000066000000E00000007C020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1465</RegID>
        <PaneID>1465</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000A40100007004000011020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1466</RegID>
        <PaneID>1466</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1467</RegID>
        <PaneID>1467</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1468</RegID>
        <PaneID>1468</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1506</RegID>
        <PaneID>1506</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>16384</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D040000D4000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1913</RegID>
        <PaneID>1913</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1935</RegID>
        <PaneID>1935</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000F8010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1936</RegID>
        <PaneID>1936</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1937</RegID>
        <PaneID>1937</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1939</RegID>
        <PaneID>1939</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1940</RegID>
        <PaneID>1940</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1941</RegID>
        <PaneID>1941</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>1942</RegID>
        <PaneID>1942</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>195</RegID>
        <PaneID>195</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0300000066000000E00000007C020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>196</RegID>
        <PaneID>196</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0300000066000000E00000007C020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>197</RegID>
        <PaneID>197</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000AD0200000006000006030000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>198</RegID>
        <PaneID>198</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000900100007004000011020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>199</RegID>
        <PaneID>199</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000B00200006D040000ED020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>203</RegID>
        <PaneID>203</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>8192</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C40000006300000070040000BC000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>204</RegID>
        <PaneID>204</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>221</RegID>
        <PaneID>221</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000000000000000000000000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>0A0000000A0000006E0000006E000000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>2506</RegID>
        <PaneID>2506</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B00300006300000070040000A0010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>2507</RegID>
        <PaneID>2507</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000A401000070040000FD010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>343</RegID>
        <PaneID>343</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>346</RegID>
        <PaneID>346</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35141</RegID>
        <PaneID>35141</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C40000006300000070040000BC000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35824</RegID>
        <PaneID>35824</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35885</RegID>
        <PaneID>35885</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35886</RegID>
        <PaneID>35886</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35887</RegID>
        <PaneID>35887</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35888</RegID>
        <PaneID>35888</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35889</RegID>
        <PaneID>35889</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35890</RegID>
        <PaneID>35890</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35891</RegID>
        <PaneID>35891</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35892</RegID>
        <PaneID>35892</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35893</RegID>
        <PaneID>35893</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35894</RegID>
        <PaneID>35894</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35895</RegID>
        <PaneID>35895</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35896</RegID>
        <PaneID>35896</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35897</RegID>
        <PaneID>35897</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35898</RegID>
        <PaneID>35898</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35899</RegID>
        <PaneID>35899</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35900</RegID>
        <PaneID>35900</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35901</RegID>
        <PaneID>35901</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35902</RegID>
        <PaneID>35902</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35903</RegID>
        <PaneID>35903</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35904</RegID>
        <PaneID>35904</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>35905</RegID>
        <PaneID>35905</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>38003</RegID>
        <PaneID>38003</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0300000066000000BD000000F8010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>38007</RegID>
        <PaneID>38007</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000AD0200007004000006030000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>436</RegID>
        <PaneID>436</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000B00200006D040000ED020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>437</RegID>
        <PaneID>437</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>440</RegID>
        <PaneID>440</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>463</RegID>
        <PaneID>463</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000B00200006D040000ED020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>466</RegID>
        <PaneID>466</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>32768</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000B00200006D040000ED020000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000006D01000058020000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>470</RegID>
        <PaneID>470</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>C7000000660000006D040000A3000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A1000000C20200000E010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50000</RegID>
        <PaneID>50000</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50001</RegID>
        <PaneID>50001</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50002</RegID>
        <PaneID>50002</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50003</RegID>
        <PaneID>50003</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50004</RegID>
        <PaneID>50004</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50005</RegID>
        <PaneID>50005</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50006</RegID>
        <PaneID>50006</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50007</RegID>
        <PaneID>50007</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50008</RegID>
        <PaneID>50008</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50009</RegID>
        <PaneID>50009</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50010</RegID>
        <PaneID>50010</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50011</RegID>
        <PaneID>50011</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50012</RegID>
        <PaneID>50012</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50013</RegID>
        <PaneID>50013</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50014</RegID>
        <PaneID>50014</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50015</RegID>
        <PaneID>50015</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50016</RegID>
        <PaneID>50016</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50017</RegID>
        <PaneID>50017</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50018</RegID>
        <PaneID>50018</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>50019</RegID>
        <PaneID>50019</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>B3030000660000006D04000016010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>59392</RegID>
        <PaneID>59392</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>8192</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>0000000000000000D10300001C000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>0A0000000A0000006E0000006E000000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>59393</RegID>
        <PaneID>0</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000060300000006000019030000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>0A0000000A0000006E0000006E000000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>59399</RegID>
        <PaneID>59399</PaneID>
        <IsVisible>1</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>8192</RecentFrameAlignment>
        <RecentRowIndex>1</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>000000001C000000E701000038000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>0A0000000A0000006E0000006E000000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>59400</RegID>
        <PaneID>59400</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>8192</RecentFrameAlignment>
        <RecentRowIndex>2</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>00000000380000006F02000054000000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>0A0000000A0000006E0000006E000000</Data>
        </RectRecentFloat>
      </Window>
      <Window>
        <RegID>824</RegID>
        <PaneID>824</PaneID>
        <IsVisible>0</IsVisible>
        <IsFloating>0</IsFloating>
        <IsTabbed>0</IsTabbed>
        <IsActivated>0</IsActivated>
        <MRUWidth>32767</MRUWidth>
        <PinState>0</PinState>
        <RecentFrameAlignment>4096</RecentFrameAlignment>
        <RecentRowIndex>0</RecentRowIndex>
        <RectRecentDocked>
          <Len>16</Len>
          <Data>03000000A70100006D040000E4010000</Data>
        </RectRecentDocked>
        <RectRecentFloat>
          <Len>16</Len>
          <Data>8A000000A10000004A0100003F010000</Data>
        </RectRecentFloat>
      </Window>
      <DockMan>
        <Len>3312</Len>
        <Data>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</Data>
      </DockMan>
      <ToolBar>
        <RegID>59392</RegID>
        <Name>File</Name>
        <Buttons>
          <Len>2537</Len>
          <Data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ata>
        </Buttons>
        <OriginalItems>
          <Len>1423</Len>
          <Data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ata>
        </OriginalItems>
        <OrigResetItems>
          <Len>1423</Len>
          <Data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ata>
        </OrigResetItems>
      </ToolBar>
      <ToolBar>
        <RegID>59399</RegID>
        <Name>Build</Name>
        <Buttons>
          <Len>986</Len>
          <Data>00200000010000001000FFFF01001100434D4643546F6F6C426172427574746F6ECF7F0000000000001C0000000000000000000000000000000001000000010000000180D07F0000000002001D000000000000000000000000000000000100000001000000018030800000000000001E000000000000000000000000000000000100000001000000FFFF01001500434D4643546F6F6C4261724D656E75427574746F6EC7040000000000006A0000000C4261746368204275696C2664000000000000000000000000010000000100000000000000000000000100000004000580C7040000000000006A0000000C4261746368204275696C266400000000000000000000000001000000010000000000000000000000010000000000058046070000000000006B0000000D42617463682052656275696C640000000000000000000000000100000001000000000000000000000001000000000005804707000000000000FFFFFFFF0B426174636820436C65616E0100000000000000000000000100000001000000000000000000000001000000000005809E8A0000000000001F0000000F4261746326682053657475702E2E2E000000000000000000000000010000000100000000000000000000000100000000000180D17F0000000004002000000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF00000000000000000000000000010000000100000001804C8A0000000000002100000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF000000000000000000000000000100000001000000FFFF01001900434D4643546F6F6C426172436F6D626F426F78427574746F6EBA00000000000000000000000000000000000000000000000001000000010000009600000003002050000000000D4361725F586966656E675F4634960000000000000001000D4361725F586966656E675F4634000000000180EB880000000000002200000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF0000000000000000000000000001000000010000000180C07F000000000000230000000000000000000000000000000001000000010000000180B08A000000000400240000000000000000000000000000000001000000010000000180A8010000000000004E00000000000000000000000000000000010000000100000001807202000000000000530000000000000000000000000000000001000000010000000180BE010000000000005000000000000000000000000000000000010000000100000000000000054275696C64FF7F0000</Data>
        </Buttons>
        <OriginalItems>
          <Len>583</Len>
          <Data>1000FFFF01001100434D4643546F6F6C426172427574746F6ECF7F000000000000FFFFFFFF0001000000000000000100000000000000010000000180D07F000000000000FFFFFFFF00010000000000000001000000000000000100000001803080000000000000FFFFFFFF00010000000000000001000000000000000100000001809E8A000000000000FFFFFFFF0001000000000000000100000000000000010000000180D17F000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001804C8A000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001806680000000000000FFFFFFFF0001000000000000000100000000000000010000000180EB88000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF0000000000000000000100000000000000010000000180C07F000000000000FFFFFFFF0001000000000000000100000000000000010000000180B08A000000000000FFFFFFFF0001000000000000000100000000000000010000000180A801000000000000FFFFFFFF00010000000000000001000000000000000100000001807202000000000000FFFFFFFF0001000000000000000100000000000000010000000180BE01000000000000FFFFFFFF000100000000000000010000000000000001000000</Data>
        </OriginalItems>
        <OrigResetItems>
          <Len>583</Len>
          <Data>1000FFFF01001100434D4643546F6F6C426172427574746F6ECF7F000000000000000000000000000000000000000000000001000000010000000180D07F00000000000001000000000000000000000000000000000100000001000000018030800000000000000200000000000000000000000000000000010000000100000001809E8A000000000000030000000000000000000000000000000001000000010000000180D17F0000000000000400000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF00000000000000000000000000010000000100000001804C8A0000000000000500000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF00000000000000000000000000010000000100000001806680000000000000060000000000000000000000000000000001000000010000000180EB880000000000000700000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF0000000000000000000000000001000000010000000180C07F000000000000080000000000000000000000000000000001000000010000000180B08A000000000000090000000000000000000000000000000001000000010000000180A8010000000000000A000000000000000000000000000000000100000001000000018072020000000000000B0000000000000000000000000000000001000000010000000180BE010000000000000C000000000000000000000000000000000100000001000000</Data>
        </OrigResetItems>
      </ToolBar>
      <ToolBar>
        <RegID>59400</RegID>
        <Name>Debug</Name>
        <Buttons>
          <Len>2373</Len>
          <Data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ata>
        </Buttons>
        <OriginalItems>
          <Len>898</Len>
          <Data>1900FFFF01001100434D4643546F6F6C426172427574746F6ECC88000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001801780000000000000FFFFFFFF00010000000000000001000000000000000100000001801D80000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001801A80000000000000FFFFFFFF00010000000000000001000000000000000100000001801B80000000000000FFFFFFFF0001000000000000000100000000000000010000000180E57F000000000000FFFFFFFF00010000000000000001000000000000000100000001801C80000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001800089000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF0000000000000000000100000000000000010000000180E48B000000000000FFFFFFFF0001000000000000000100000000000000010000000180F07F000000000000FFFFFFFF0001000000000000000100000000000000010000000180E888000000000000FFFFFFFF00010000000000000001000000000000000100000001803B01000000000000FFFFFFFF0001000000000000000100000000000000010000000180BB8A000000000000FFFFFFFF0001000000000000000100000000000000010000000180D88B000000000000FFFFFFFF0001000000000000000100000000000000010000000180D28B000000000000FFFFFFFF00010000000000000001000000000000000100000001809307000000000000FFFFFFFF0001000000000000000100000000000000010000000180658A000000000000FFFFFFFF0001000000000000000100000000000000010000000180C18A000000000000FFFFFFFF0001000000000000000100000000000000010000000180EE8B000000000000FFFFFFFF00010000000000000001000000000000000100000001800000000000000000FFFFFFFF00000000000000000001000000000000000100000001800189000000000000FFFFFFFF000100000000000000010000000000000001000000</Data>
        </OriginalItems>
        <OrigResetItems>
          <Len>898</Len>
          <Data>1900FFFF01001100434D4643546F6F6C426172427574746F6ECC880000000000000000000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF000000000000000000000000000100000001000000018017800000000000000100000000000000000000000000000000010000000100000001801D800000000000000200000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF00000000000000000000000000010000000100000001801A800000000000000300000000000000000000000000000000010000000100000001801B80000000000000040000000000000000000000000000000001000000010000000180E57F0000000000000500000000000000000000000000000000010000000100000001801C800000000000000600000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF000000000000000000000000000100000001000000018000890000000000000700000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF0000000000000000000000000001000000010000000180E48B000000000000080000000000000000000000000000000001000000010000000180F07F000000000000090000000000000000000000000000000001000000010000000180E8880000000000000A00000000000000000000000000000000010000000100000001803B010000000000000B0000000000000000000000000000000001000000010000000180BB8A0000000000000C0000000000000000000000000000000001000000010000000180D88B0000000000000D0000000000000000000000000000000001000000010000000180D28B0000000000000E000000000000000000000000000000000100000001000000018093070000000000000F0000000000000000000000000000000001000000010000000180658A000000000000100000000000000000000000000000000001000000010000000180C18A000000000000110000000000000000000000000000000001000000010000000180EE8B0000000000001200000000000000000000000000000000010000000100000001800000000001000000FFFFFFFF0000000000000000000000000001000000010000000180018900000000000013000000000000000000000000000000000100000001000000</Data>
        </OrigResetItems>
      </ToolBar>
      <ControlBarsSummary>
        <Bars>0</Bars>
        <ScreenCX>1536</ScreenCX>
        <ScreenCY>864</ScreenCY>
      </ControlBarsSummary>
    </ViewEx>
  </WinLayoutEx>

  <MDIGroups>
    <Orientation>1</Orientation>
    <ActiveMDIGroup>0</ActiveMDIGroup>
    <MDIGroup>
      <Size>100</Size>
      <ActiveTab>65</ActiveTab>
      <Doc>
        <Name>..\APP\uart_app.c</Name>
        <ColumnNumber>5</ColumnNumber>
        <TopLine>52</TopLine>
        <CurrentLine>77</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/gpio.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>70</TopLine>
        <CurrentLine>86</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\gray_app.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>21</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\scheduler.c</Name>
        <ColumnNumber>7</ColumnNumber>
        <TopLine>41</TopLine>
        <CurrentLine>44</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\LED\led_driver.c</Name>
        <ColumnNumber>28</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>18</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\led_app.c</Name>
        <ColumnNumber>2</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>8</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/main.c</Name>
        <ColumnNumber>16</ColumnNumber>
        <TopLine>4</TopLine>
        <CurrentLine>21</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/usart.c</Name>
        <ColumnNumber>14</ColumnNumber>
        <TopLine>13</TopLine>
        <CurrentLine>21</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\encoder_app.c</Name>
        <ColumnNumber>3</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>18</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\MyDefine.h</Name>
        <ColumnNumber>13</ColumnNumber>
        <TopLine>12</TopLine>
        <CurrentLine>43</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\Hwt101\hwt101_driver.c</Name>
        <ColumnNumber>13</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>8</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Grayscale\hardware_iic.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Uart\ringbuffer.c</Name>
        <ColumnNumber>44</ColumnNumber>
        <TopLine>6</TopLine>
        <CurrentLine>22</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Uart\uart_driver.c</Name>
        <ColumnNumber>16</ColumnNumber>
        <TopLine>19</TopLine>
        <CurrentLine>33</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\motor_app.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>15</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\uart2_app.c</Name>
        <ColumnNumber>24</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>uart_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>13</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Uart\uart_driver.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>4</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\Ebtn\ebtn_driver.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>4</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\gray_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>4</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\led_app.h</Name>
        <ColumnNumber>20</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>6</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/tim.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/dma.c</Name>
        <ColumnNumber>48</ColumnNumber>
        <TopLine>12</TopLine>
        <CurrentLine>13</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Uart\uart2_driver.c</Name>
        <ColumnNumber>18</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\uart2_app.c</Name>
        <ColumnNumber>21</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>21</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\Uart\uart_driver_new.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\uart2_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>10</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\uart3_driver.c</Name>
        <ColumnNumber>12</ColumnNumber>
        <TopLine>23</TopLine>
        <CurrentLine>50</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>uart3_driver.h</Name>
        <ColumnNumber>15</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>6</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Components\Uart\uart2_driver.h</Name>
        <ColumnNumber>18</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>4</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\Ebtn\ebtn_driver.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\uart3_app.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>16</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\MDK-ARM\uart3_app.h</Name>
        <ColumnNumber>6</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>9</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Core\Inc\usart.h</Name>
        <ColumnNumber>9</ColumnNumber>
        <TopLine>28</TopLine>
        <CurrentLine>49</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/i2c.c</Name>
        <ColumnNumber>43</ColumnNumber>
        <TopLine>65</TopLine>
        <CurrentLine>79</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\esp01_app.c</Name>
        <ColumnNumber>1</ColumnNumber>
        <TopLine>19</TopLine>
        <CurrentLine>39</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\..\MDK-ARM\esp01_app.h</Name>
        <ColumnNumber>4</ColumnNumber>
        <TopLine>18</TopLine>
        <CurrentLine>19</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\GPS_app.c</Name>
        <ColumnNumber>21</ColumnNumber>
        <TopLine>19</TopLine>
        <CurrentLine>20</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\MDK-ARM\GPS_app.h</Name>
        <ColumnNumber>51</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>25</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\IIC_app.c</Name>
        <ColumnNumber>15</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\IIC_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>12</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\MPU6050\mpu6050_driver.c</Name>
        <ColumnNumber>8</ColumnNumber>
        <TopLine>154</TopLine>
        <CurrentLine>158</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\APP\mpu6050_app.c</Name>
        <ColumnNumber>4</ColumnNumber>
        <TopLine>73</TopLine>
        <CurrentLine>86</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\mpu6050_app.h</Name>
        <ColumnNumber>18</ColumnNumber>
        <TopLine>53</TopLine>
        <CurrentLine>67</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\RTE\dqw</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>7</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\include\stdlib.h</Name>
        <ColumnNumber>23</ColumnNumber>
        <TopLine>5</TopLine>
        <CurrentLine>21</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\navigation_app.c</Name>
        <ColumnNumber>10</ColumnNumber>
        <TopLine>262</TopLine>
        <CurrentLine>293</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\uart4_app.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>7</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\uart4_app.h</Name>
        <ColumnNumber>21</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>6</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\uart4_driver.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\uart4_driver.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f407xx.h</Name>
        <ColumnNumber>26</ColumnNumber>
        <TopLine>1059</TopLine>
        <CurrentLine>1074</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/stm32f4xx_it.c</Name>
        <ColumnNumber>32</ColumnNumber>
        <TopLine>52</TopLine>
        <CurrentLine>61</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\uart6_driver.c</Name>
        <ColumnNumber>54</ColumnNumber>
        <TopLine>12</TopLine>
        <CurrentLine>27</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\uart6_driver.h</Name>
        <ColumnNumber>38</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>8</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>../Core/Src/stm32f4xx_hal_msp.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\PID\pid.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>1</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\uart6_app.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>12</TopLine>
        <CurrentLine>32</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>uart6_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>7</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\APP\uart_app.h</Name>
        <ColumnNumber>10</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>7</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\Components\Ebtn\ebtn.c</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>11</TopLine>
        <CurrentLine>27</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\tft_app.c</Name>
        <ColumnNumber>6</ColumnNumber>
        <TopLine>59</TopLine>
        <CurrentLine>87</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>..\MDK-ARM\tft_app.h</Name>
        <ColumnNumber>0</ColumnNumber>
        <TopLine>1</TopLine>
        <CurrentLine>6</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\lcd_init_hal.c</Name>
        <ColumnNumber>47</ColumnNumber>
        <TopLine>15</TopLine>
        <CurrentLine>16</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>.\lcd_display_hal.c</Name>
        <ColumnNumber>14</ColumnNumber>
        <TopLine>250</TopLine>
        <CurrentLine>251</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
      <Doc>
        <Name>C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\touch_driver.c</Name>
        <ColumnNumber>6</ColumnNumber>
        <TopLine>46</TopLine>
        <CurrentLine>17</CurrentLine>
        <Folding>1</Folding>
        <ContractedFolders></ContractedFolders>
        <PaneID>0</PaneID>
      </Doc>
    </MDIGroup>
  </MDIGroups>

</ProjectGui>
