/**
 * 分页导航显示系统
 * 支持触摸翻页的完整导航信息显示
 */

#ifndef __NAVIGATION_PAGING_H__
#define __NAVIGATION_PAGING_H__

#include "navigation_types.h"
#include "lcd_display_hal.h"

// 全局变量声明
extern NavigationPaging_t g_nav_paging;

// 函数声明
void NavPaging_Init(void);
void NavPaging_LoadDynamicRoute(void);
void NavPaging_LoadIdleState(void);
void NavPaging_UpdateDynamicData(void);
void NavPaging_UpdateCurrentStep(void);
uint8_t NavPaging_GetDirectionFromBearing(float bearing);
float NavPaging_CalculateDistance(float lat1, float lon1, float lat2, float lon2);
uint8_t NavPaging_CalculateCurrentStepFromDistance(float distance_to_dest);

// 检测器数据获取函数
void NavPaging_FetchDetectorData(void);
void NavPaging_FetchWandaData(void);
void NavPaging_FetchAcademyData(void);
void NavPaging_FetchSportsData(void);
void NavPaging_FetchTrainData(void);
void NavPaging_FetchHospitalData(void);

// 检测器通信函数
uint8_t NavPaging_RequestDetectorData(const char* destination, NavigationDetectorData_t* data);
uint8_t NavPaging_GetDetectorRealTimeData(const char* destination, NavigationDetectorData_t* data);
uint8_t NavPaging_ParseDetectorResponse(const char* response, NavigationDetectorData_t* data);

// 改进的显示函数
void NavPaging_DrawImprovedArrow(uint8_t direction, uint16_t x, uint16_t y, uint16_t color);
void NavPaging_NextPage(void);
void NavPaging_PrevPage(void);
void NavPaging_Display(void);
void NavPaging_AutoFlip(void);
void NavPaging_DrawHeader(void);
void NavPaging_DrawFooter(void);
void NavPaging_DrawSteps(void);
void NavPaging_DrawPageIndicator(void);
void NavPaging_DrawAutoFlipInfo(void);
uint8_t NavPaging_GetStepsPerPage(void);

// 导航步骤管理
void NavPaging_AddStep(uint8_t step_num, const char* instruction, const char* distance,
                       const char* road_name, uint8_t direction);
void NavPaging_SetCurrentStep(uint8_t step_num);
void NavPaging_CompleteStep(uint8_t step_num);

// 绘制函数
void NavPaging_DrawSingleStep(NavigationStep_t* step, uint16_t y_pos, uint16_t color);
void NavPaging_DrawDirectionArrow(uint8_t direction, uint16_t x, uint16_t y);

#endif /* __NAVIGATION_PAGING_H__ */
