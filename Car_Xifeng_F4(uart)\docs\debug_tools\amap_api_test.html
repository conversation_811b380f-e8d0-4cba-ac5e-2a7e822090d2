<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>高德地图API测试</title>
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.15&key=hh&plugin=AMap.Walking"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #container { width: 100%; height: 400px; border: 1px solid #ccc; }
        .controls { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🗺️ 高德地图API测试</h1>
    
    <div class="controls">
        <button onclick="testWalkingRoute()">🚶 测试步行导航API</button>
        <button onclick="testManualRoute()">📍 手动测试路线</button>
        <button onclick="clearLog()">🧹 清空日志</button>
    </div>
    
    <div id="container"></div>
    
    <div class="log" id="log">
        <div>📋 API测试日志：</div>
    </div>

    <script>
        var map;
        var walking;
        
        // 初始化地图
        function initMap() {
            log('🚀 初始化高德地图...');
            
            map = new AMap.Map('container', {
                resizeEnable: true,
                center: [112.676903, 26.881201], // 衡阳师范学院
                zoom: 15
            });
            
            log('✅ 地图初始化成功');
            log('🔑 使用API Key: hh');
            log('📍 地图中心: 衡阳师范学院');
        }
        
        // 测试步行导航API
        function testWalkingRoute() {
            log('🧪 开始测试高德地图步行导航API...');
            
            if (!map) {
                log('❌ 地图未初始化');
                return;
            }
            
            // 测试坐标 (衡阳师范学院 -> 万达广场)
            var startLon = 112.676903;  // 起点经度
            var startLat = 26.881201;   // 起点纬度
            var endLon = 112.675797;    // 终点经度
            var endLat = 26.886900;     // 终点纬度
            
            log('📍 起点: ' + startLon + ', ' + startLat);
            log('📍 终点: ' + endLon + ', ' + endLat);
            
            // 创建Walking实例
            walking = new AMap.Walking({
                map: map
            });
            
            log('🔧 Walking实例已创建');
            
            // 调用search方法
            walking.search(
                new AMap.LngLat(startLon, startLat), 
                new AMap.LngLat(endLon, endLat), 
                function(status, result) {
                    log('📡 API回调状态: ' + status);
                    
                    if (status === 'complete') {
                        log('✅ 高德地图API调用成功！');
                        log('📊 路径数据: ' + JSON.stringify(result, null, 2));
                        
                        if (result.routes && result.routes.length > 0) {
                            var route = result.routes[0];
                            var distance = (route.distance / 1000).toFixed(1);
                            var time = Math.round(route.time / 60);
                            
                            log('📏 距离: ' + distance + 'km');
                            log('⏱️ 时间: ' + time + '分钟');
                            log('🎯 API调用完全成功！');
                        }
                    } else {
                        log('❌ 高德地图API调用失败: ' + status);
                        log('🔍 可能的原因: API Key无效、网络问题、坐标错误');
                    }
                }
            );
            
            log('📤 API请求已发送，等待响应...');
        }
        
        // 手动测试路线
        function testManualRoute() {
            log('🔧 手动测试路线规划...');
            
            // 模拟从ThingSpeak获取的数据
            var testData = "WANDA_112.676903_26.881201_112.675797_26.886900";
            log('📥 模拟导航数据: ' + testData);
            
            // 解析数据
            var coords = testData.substring(6).split('_');
            if (coords.length >= 4) {
                var startLon = parseFloat(coords[0]);
                var startLat = parseFloat(coords[1]);
                var endLon = parseFloat(coords[2]);
                var endLat = parseFloat(coords[3]);
                
                log('✅ 数据解析成功');
                log('📍 起点: ' + startLon + ', ' + startLat);
                log('📍 终点: ' + endLon + ', ' + endLat);
                
                // 调用API
                testWalkingRoute();
            } else {
                log('❌ 数据格式错误');
            }
        }
        
        // 日志函数
        function log(message) {
            var logDiv = document.getElementById('log');
            var time = new Date().toLocaleTimeString();
            logDiv.innerHTML += '<div>[' + time + '] ' + message + '</div>';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 API测试日志：</div>';
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            log('🌐 页面加载完成');
            initMap();
        };
    </script>
</body>
</html>
