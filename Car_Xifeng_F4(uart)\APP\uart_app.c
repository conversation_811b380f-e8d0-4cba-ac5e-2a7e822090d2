#include "uart_app.h"
#include "navigation_app.h"
#include "esp01_app.h"

#define GPS_UPLOAD_INTERVAL 10000  // GPS上传间隔（毫秒）

extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE];
extern uint8_t ring_buffer_input[BUFFER_SIZE];
extern struct rt_ringbuffer ring_buffer;
extern uint8_t uart_data_buffer[BUFFER_SIZE];

void Uart_Init(void)
{
  // 最基本的启动信息
  my_printf(&huart1, "\r\n=== 系统启动 ===\r\n");

  // 初始化环形缓冲区
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);

  // 启动DMA接收
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE);
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);

  my_printf(&huart1, "系统就绪，请输入命令: ");
}


void Uart_Task(void)
{
  static uint32_t last_heartbeat = 0;
  static uint32_t heartbeat_count = 0;
  uint32_t current_time = HAL_GetTick();

  // 每5秒发送一次心跳，证明系统在运行
  if (current_time - last_heartbeat > 5000) {
    last_heartbeat = current_time;
    heartbeat_count++;
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);  // 切换LED状态

    // 每60秒发送一次简单心跳信息
    if (heartbeat_count % 12 == 1) {
      my_printf(&huart1, "\r\n系统运行正常\r\n");
    }
  }

  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);

  if(uart_data_len > 0)
  {
    // 调试：显示接收到的数据长度
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);  // LED闪烁表示处理命令

    // 获取数据
    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';

    // 清理输入数据，移除换行符
    char clean_command[64];
    strncpy(clean_command, (char*)uart_data_buffer, sizeof(clean_command) - 1);
    clean_command[sizeof(clean_command) - 1] = '\0';

    // 移除所有可能的换行符和回车符
    char* newline = strchr(clean_command, '\r');
    if (newline) *newline = '\0';
    newline = strchr(clean_command, '\n');
    if (newline) *newline = '\0';

    // 忽略空命令
    if (strlen(clean_command) == 0) {
      return;
    }

    my_printf(&huart1, "\r\n> %s\r\n", clean_command);

    // 处理导航命令
    if (strcmp(clean_command, "wangda") == 0 || strcmp(clean_command, "wanda") == 0)
    {
        my_printf(&huart1, "启动万达导航\r\n");
        Navigation_StartNavigation("wanda");
        return;
    }
    else if (strcmp(clean_command, "nav_test1") == 0)
    {
        my_printf(&huart1, "导航到万达\r\n");
        Navigation_StartNavigation("wanda");
        return;
    }
    else if (strcmp(clean_command, "nav_test6") == 0)
    {
        my_printf(&huart1, "启动衡阳师范学院导航\r\n");
        Navigation_StartNavigation("6");
        return;
    }
    else if (strcmp(clean_command, "hello") == 0)
    {
        my_printf(&huart1, "Hello! 系统正常\r\n");
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);
    }

    else if (strcmp(clean_command, "esp_status") == 0)
    {
        ESP01_State_t state = esp01_GetState();
        switch(state)
        {
            case ESP01_STATE_CONNECTED:
                my_printf(&huart1, "ESP01: 已连接\r\n");
                break;
            case ESP01_STATE_CONNECTING:
                my_printf(&huart1, "ESP01: 连接中\r\n");
                break;
            case ESP01_STATE_ERROR:
                my_printf(&huart1, "ESP01: 错误\r\n");
                break;
            default:
                my_printf(&huart1, "ESP01: 空闲\r\n");
                break;
        }
    }
    else if (strcmp(clean_command, "test_upload") == 0)
    {
        my_printf(&huart1, "手动上传GPS数据\r\n");
        esp01_UploadGPSData();
    }
    else if (strcmp(clean_command, "esp_reset") == 0)
    {
        my_printf(&huart1, "手动重置ESP01\r\n");
        esp01_Reset();
    }
    else if (strcmp(clean_command, "esp_test") == 0)
    {
        my_printf(&huart1, "发送AT测试命令\r\n");
        Uart2_Printf(&huart2, "AT\r\n");
    }
    else if (strcmp(clean_command, "get_location") == 0)
    {
        float lat, lon, alt;
        esp01_GetRealLocation(&lat, &lon, &alt);
        my_printf(&huart1, "当前GPS位置:\r\n");
        my_printf(&huart1, "  纬度: %.6f°N\r\n", lat);
        my_printf(&huart1, "  经度: %.6f°E\r\n", lon);
        my_printf(&huart1, "  海拔: %.1f米\r\n", alt);
    }
    else if (strcmp(clean_command, "wifi_reconnect") == 0)
    {
        my_printf(&huart1, "🔄 重新连接WiFi热点...\r\n");
        esp01_ReconnectWiFi();
    }
    else if (strcmp(clean_command, "wifi_check") == 0)
    {
        my_printf(&huart1, "🔍 检查WiFi连接状态...\r\n");
        esp01_CheckConnection();
    }
    else if (strcmp(clean_command, "esp_reset") == 0)
    {
        my_printf(&huart1, "🔄 重置ESP01模块...\r\n");
        esp01_Reset();
    }
    else if (strcmp(clean_command, "lcd_test") == 0)
    {
        my_printf(&huart1, "🖥️ 执行LCD测试...\r\n");
        extern void tft_BasicTest(void);
        tft_BasicTest();
        my_printf(&huart1, "✅ LCD测试完成\r\n");
    }
    else if (strcmp(clean_command, "lcd_pin_test") == 0)
    {
        my_printf(&huart1, "📌 LCD引脚状态检查:\r\n");
        my_printf(&huart1, "  PB13(SCLK): %s\r\n", HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_13) ? "HIGH" : "LOW");
        my_printf(&huart1, "  PB15(MOSI): %s\r\n", HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_15) ? "HIGH" : "LOW");
        my_printf(&huart1, "  PD4(RES):   %s\r\n", HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_4) ? "HIGH" : "LOW");
        my_printf(&huart1, "  PD15(DC):   %s\r\n", HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_15) ? "HIGH" : "LOW");
        my_printf(&huart1, "  PD1(BLK):   %s\r\n", HAL_GPIO_ReadPin(GPIOD, GPIO_PIN_1) ? "HIGH" : "LOW");

        my_printf(&huart1, "🔄 测试引脚切换...\r\n");
        for(int i = 0; i < 3; i++) {
            HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_SET);   // 背光开
            HAL_Delay(500);
            HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_RESET); // 背光关
            HAL_Delay(500);
        }
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_SET); // 最后开启背光
        my_printf(&huart1, "✅ 引脚测试完成\r\n");
    }
    else if (strcmp(clean_command, "help") == 0)
    {
        my_printf(&huart1, "\r\n📋 可用命令:\r\n");
        my_printf(&huart1, "  🚗 导航: hello, wanda, nav_test1-4\r\n");
        my_printf(&huart1, "  📡 ESP01: esp_status, wifi_check\r\n");
        my_printf(&huart1, "  🔧 维护: wifi_reconnect, esp_reset\r\n");
        my_printf(&huart1, "  📍 GPS: get_location, test_upload\r\n");
        my_printf(&huart1, "  🖥️ LCD: lcd_test\r\n");
        my_printf(&huart1, "  ❓ 帮助: help\r\n");
    }
    else
    {
        // 尝试导航系统处理
        Navigation_ProcessCommand(clean_command);
    }

    // 清空缓冲区
    memset(uart_data_buffer, 0, uart_data_len);

    // 强制清空环形缓冲区，确保没有残留数据
    rt_ringbuffer_reset(&ring_buffer);
  }
}


