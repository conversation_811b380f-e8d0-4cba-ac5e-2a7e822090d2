/**
 * 导航系统类型定义
 * 避免循环依赖的独立类型定义文件
 */

#ifndef __NAVIGATION_TYPES_H__
#define __NAVIGATION_TYPES_H__

#include <stdint.h>

// 触摸事件类型
typedef enum {
    TOUCH_EVENT_NONE = 0,
    TOUCH_EVENT_PRESS,      // 按下
    TOUCH_EVENT_RELEASE,    // 释放
    TOUCH_EVENT_MOVE,       // 移动
    TOUCH_EVENT_SWIPE_UP,   // 向上滑动
    TOUCH_EVENT_SWIPE_DOWN, // 向下滑动
    TOUCH_EVENT_SWIPE_LEFT, // 向左滑动
    TOUCH_EVENT_SWIPE_RIGHT // 向右滑动
} TouchEvent_t;

// 导航步骤结构体
typedef struct {
    uint8_t step_num;           // 步骤编号
    char instruction[64];       // 导航指令
    char distance[16];          // 距离信息
    char road_name[32];         // 道路名称
    uint8_t direction;          // 方向类型：0-直行，1-左转，2-右转，3-到达
    uint8_t completed;          // 是否已完成
} NavigationStep_t;

// 触摸点结构体
typedef struct {
    uint16_t x;          // X坐标
    uint16_t y;          // Y坐标
    uint8_t valid;       // 触摸点是否有效
    uint8_t pressure;    // 压力值（0-255）
} TouchPoint_t;

// 触摸状态结构体
typedef struct {
    TouchPoint_t current;     // 当前触摸点
    TouchPoint_t last;        // 上一次触摸点
    TouchEvent_t event;       // 触摸事件
    uint32_t press_time;      // 按下时间
    uint32_t release_time;    // 释放时间
    uint8_t gesture_detected; // 手势检测标志
} TouchState_t;

// 触摸区域定义
typedef struct {
    uint16_t x1, y1;     // 左上角
    uint16_t x2, y2;     // 右下角
    uint8_t id;          // 区域ID
    const char* name;    // 区域名称
} TouchArea_t;

// 分页显示状态
typedef struct {
    NavigationStep_t* steps;    // 导航步骤数组
    uint8_t total_steps;        // 总步骤数
    uint8_t current_page;       // 当前页码
    uint8_t steps_per_page;     // 每页显示的步骤数
    uint8_t total_pages;        // 总页数
    uint8_t current_step;       // 当前执行的步骤
    float total_distance;       // 总距离(km)
    uint16_t estimated_time;    // 预计时间(分钟)
} NavigationPaging_t;

// 页面类型
typedef enum {
    NAV_PAGE_OVERVIEW = 0,      // 概览页
    NAV_PAGE_STEPS,             // 步骤页
    NAV_PAGE_SETTINGS           // 设置页
} NavigationPageType_t;

// 常用触摸区域ID
#define TOUCH_AREA_NONE        0
#define TOUCH_AREA_PREV_PAGE   1  // 上一页
#define TOUCH_AREA_NEXT_PAGE   2  // 下一页
#define TOUCH_AREA_BACK        3  // 返回
#define TOUCH_AREA_HOME        4  // 主页
#define TOUCH_AREA_NAV_STEP    5  // 导航步骤区域

// 触摸配置
#define TOUCH_I2C_ADDR         0x38  // FT6236默认地址
#define TOUCH_MAX_POINTS       2     // 最大触摸点数
#define TOUCH_DEBOUNCE_TIME    50    // 消抖时间(ms)
#define TOUCH_SWIPE_THRESHOLD  30    // 滑动阈值(像素)
#define TOUCH_LONG_PRESS_TIME  1000  // 长按时间(ms)

#endif /* __NAVIGATION_TYPES_H__ */
