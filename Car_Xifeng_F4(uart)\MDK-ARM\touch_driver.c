/**
 * 电容触摸屏驱动实现
 * 支持FT6236等常见触摸IC
 */

#include "touch_driver.h"
#include "hardware_iic.h"
#include <stdlib.h>  // 包含abs函数的声明（推荐，stdlib.h是abs的标准头文件）
#include <string.h>  // 包含memset函数的声明

// 全局变量
static TouchState_t g_touch_state = {0};
static TouchArea_t g_touch_areas[10] = {0}; // 最多10个触摸区域
static uint8_t g_area_count = 0;
static uint8_t g_touch_initialized = 0;

// 触摸校准参数
static uint16_t touch_x_min = 0, touch_x_max = 240;
static uint16_t touch_y_min = 0, touch_y_max = 320;

/**
 * @brief 触摸屏初始化
 */
void Touch_Init(void)
{
    // 初始化I2C（如果还没初始化）
    // I2C已在其他地方初始化
    
    // 清除触摸状态
    memset(&g_touch_state, 0, sizeof(TouchState_t));
    
    // 设置默认校准参数
    Touch_SetCalibration(0, 240, 0, 320);
    
    // 注册默认触摸区域
    Touch_RegisterArea(TOUCH_AREA_PREV_PAGE, 0, 0, 60, 40, "PrevPage");
    Touch_RegisterArea(TOUCH_AREA_NEXT_PAGE, 180, 0, 240, 40, "NextPage");
    Touch_RegisterArea(TOUCH_AREA_BACK, 0, 280, 60, 320, "Back");
    Touch_RegisterArea(TOUCH_AREA_HOME, 180, 280, 240, 320, "Home");
    
    g_touch_initialized = 1;
}

/**
 * @brief 读取触摸数据（简化版本）
 */
uint8_t Touch_ReadData(TouchPoint_t* point)
{
    uint8_t touch_data[6];
    
    // 模拟触摸检测（实际项目中需要根据具体触摸IC实现）
    // 这里使用简化的检测方法
    
    // 检查是否有触摸（可以通过GPIO中断或定时扫描）
    // 这里使用模拟数据进行演示
    static uint32_t last_touch_time = 0;
    static uint8_t touch_active = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 模拟触摸事件（每5秒模拟一次触摸）
    if (current_time - last_touch_time > 5000) {
        last_touch_time = current_time;
        touch_active = !touch_active;
        
        if (touch_active) {
            // 模拟触摸坐标
            point->x = 200; // 模拟点击下一页区域
            point->y = 20;
            point->valid = 1;
            point->pressure = 128;
            return 1;
        }
    }
    
    point->valid = 0;
    return 0;
}

/**
 * @brief 触摸扫描
 */
uint8_t Touch_Scan(TouchState_t* touch_state)
{
    if (!g_touch_initialized) return 0;
    
    TouchPoint_t new_point;
    uint8_t touch_detected = Touch_ReadData(&new_point);
    
    // 保存上一次状态
    touch_state->last = touch_state->current;
    
    if (touch_detected && new_point.valid) {
        // 有触摸
        touch_state->current = new_point;
        
        if (!touch_state->last.valid) {
            // 新的触摸按下
            touch_state->event = TOUCH_EVENT_PRESS;
            touch_state->press_time = HAL_GetTick();
        } else {
            // 触摸移动
            touch_state->event = TOUCH_EVENT_MOVE;
        }
        
        return 1;
    } else {
        // 无触摸
        if (touch_state->last.valid) {
            // 触摸释放
            touch_state->event = TOUCH_EVENT_RELEASE;
            touch_state->release_time = HAL_GetTick();
            touch_state->current.valid = 0;
            
            // 检测手势
            Touch_DetectGesture(touch_state);
            return 1;
        } else {
            touch_state->event = TOUCH_EVENT_NONE;
        }
    }
    
    return 0;
}

/**
 * @brief 检测手势
 */
uint8_t Touch_DetectGesture(TouchState_t* touch_state)
{
    if (!touch_state->last.valid) return 0;
    
    int16_t dx = touch_state->current.x - touch_state->last.x;
    int16_t dy = touch_state->current.y - touch_state->last.y;
    
    // 检测滑动手势
    if (abs(dx) > TOUCH_SWIPE_THRESHOLD || abs(dy) > TOUCH_SWIPE_THRESHOLD) {
        if (abs(dx) > abs(dy)) {
            // 水平滑动
            if (dx > 0) {
                touch_state->event = TOUCH_EVENT_SWIPE_RIGHT;
            } else {
                touch_state->event = TOUCH_EVENT_SWIPE_LEFT;
            }
        } else {
            // 垂直滑动
            if (dy > 0) {
                touch_state->event = TOUCH_EVENT_SWIPE_DOWN;
            } else {
                touch_state->event = TOUCH_EVENT_SWIPE_UP;
            }
        }
        touch_state->gesture_detected = 1;
        return 1;
    }
    
    return 0;
}

/**
 * @brief 获取触摸事件
 */
TouchEvent_t Touch_GetEvent(void)
{
    TouchState_t touch_state;
    if (Touch_Scan(&touch_state)) {
        return touch_state.event;
    }
    return TOUCH_EVENT_NONE;
}

/**
 * @brief 检查是否有触摸
 */
uint8_t Touch_IsPressed(void)
{
    TouchPoint_t point;
    return Touch_ReadData(&point);
}

/**
 * @brief 获取触摸区域
 */
uint8_t Touch_GetArea(uint16_t x, uint16_t y)
{
    for (uint8_t i = 0; i < g_area_count; i++) {
        TouchArea_t* area = &g_touch_areas[i];
        if (x >= area->x1 && x <= area->x2 && y >= area->y1 && y <= area->y2) {
            return area->id;
        }
    }
    return TOUCH_AREA_NONE;
}

/**
 * @brief 注册触摸区域
 */
void Touch_RegisterArea(uint8_t id, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, const char* name)
{
    if (g_area_count < 10) {
        TouchArea_t* area = &g_touch_areas[g_area_count];
        area->id = id;
        area->x1 = x1;
        area->y1 = y1;
        area->x2 = x2;
        area->y2 = y2;
        area->name = name;
        g_area_count++;
    }
}

/**
 * @brief 清除所有触摸区域
 */
void Touch_ClearAreas(void)
{
    g_area_count = 0;
    memset(g_touch_areas, 0, sizeof(g_touch_areas));
}

/**
 * @brief 设置触摸校准参数
 */
void Touch_SetCalibration(uint16_t x_min, uint16_t x_max, uint16_t y_min, uint16_t y_max)
{
    touch_x_min = x_min;
    touch_x_max = x_max;
    touch_y_min = y_min;
    touch_y_max = y_max;
}

/**
 * @brief 触摸校准
 */
void Touch_Calibrate(void)
{
    // 触摸校准程序
    // 在实际项目中，这里会显示校准界面
    // 让用户点击屏幕四个角进行校准
    
    // 简化版本：使用默认校准参数
    Touch_SetCalibration(0, 240, 0, 320);
}
