#include "tft_app.h"
#include "lcd_quick_test.h"

// 外部变量声明
extern UART_HandleTypeDef huart1;

// 静态变量
static uint32_t display_counter = 0;
static float test_float = 0.0f;

/**
 * @brief LCD基础测试函数 - 测试基本显示功能
 */
void tft_BasicTest(void)
{
    my_printf(&huart1, "🧪 开始LCD基础测试...\r\n");

    // 测试引脚状态
    my_printf(&huart1, "🔧 测试LCD引脚状态...\r\n");

    // 手动控制背光测试
    my_printf(&huart1, "💡 背光测试 - 关闭背光\r\n");
    LCD_BLK_Clr();
    HAL_Delay(1000);
    my_printf(&huart1, "💡 背光测试 - 打开背光\r\n");
    LCD_BLK_Set();
    HAL_Delay(1000);

    // 测试1: 填充纯色 - 使用更小的区域先测试
    my_printf(&huart1, "🔴 测试红色填充 (小区域)...\r\n");
    LCD_Fill(0, 0, 50, 50, RED);
    HAL_Delay(2000);

    my_printf(&huart1, "🟢 测试绿色填充 (小区域)...\r\n");
    LCD_Fill(50, 0, 100, 50, GREEN);
    HAL_Delay(2000);

    my_printf(&huart1, "🔵 测试蓝色填充 (小区域)...\r\n");
    LCD_Fill(0, 50, 50, 100, BLUE);
    HAL_Delay(2000);

    // 测试全屏填充
    my_printf(&huart1, "⚫ 测试黑色填充 (全屏)...\r\n");
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    HAL_Delay(2000);

    my_printf(&huart1, "🔴 测试红色填充 (全屏)...\r\n");
    LCD_Fill(0, 0, LCD_W, LCD_H, RED);
    HAL_Delay(2000);

    my_printf(&huart1, "⚪ 测试白色填充 (全屏)...\r\n");
    LCD_Fill(0, 0, LCD_W, LCD_H, WHITE);
    HAL_Delay(2000);

    my_printf(&huart1, "✅ LCD基础测试完成\r\n");
}

/**
 * @brief LCD硬件诊断函数 - 诊断硬件连接问题
 */
void tft_HardwareDiagnose(void)
{
    my_printf(&huart1, "🔍 开始LCD硬件诊断...\r\n");

    // 测试各个控制引脚
    my_printf(&huart1, "📌 测试控制引脚...\r\n");

    // 测试背光引脚
    my_printf(&huart1, "💡 测试背光引脚 PD1...\r\n");
    for(int i = 0; i < 5; i++) {
        LCD_BLK_Set();
        HAL_Delay(200);
        LCD_BLK_Clr();
        HAL_Delay(200);
    }
    LCD_BLK_Set(); // 最后保持打开

    // 测试复位引脚
    my_printf(&huart1, "🔄 测试复位引脚 PD4...\r\n");
    LCD_RES_Set();
    HAL_Delay(100);
    LCD_RES_Clr();
    HAL_Delay(100);
    LCD_RES_Set();
    HAL_Delay(100);

    // 测试DC引脚
    my_printf(&huart1, "📡 测试DC引脚 PD0...\r\n");
    for(int i = 0; i < 5; i++) {
        LCD_DC_Set();
        HAL_Delay(100);
        LCD_DC_Clr();
        HAL_Delay(100);
    }
    LCD_DC_Set(); // 最后保持数据模式

    // 测试SPI引脚
    my_printf(&huart1, "🔌 测试SPI引脚 PB13(SCK), PB15(MOSI)...\r\n");
    for(int i = 0; i < 10; i++) {
        LCD_SCLK_Set();
        LCD_MOSI_Set();
        HAL_Delay(50);
        LCD_SCLK_Clr();
        LCD_MOSI_Clr();
        HAL_Delay(50);
    }

    // 发送测试数据
    my_printf(&huart1, "📤 发送测试数据...\r\n");
    LCD_WR_REG(0x00); // NOP命令
    LCD_WR_DATA8(0x55); // 测试数据
    LCD_WR_DATA8(0xAA); // 测试数据

    my_printf(&huart1, "✅ 硬件诊断完成\r\n");
}

/**
 * @brief LCD深度诊断函数 - 更详细的硬件测试
 */
void tft_DeepDiagnose(void)
{
    my_printf(&huart1, "🔬 开始LCD深度诊断...\r\n");

    // 1. 检查引脚状态
    my_printf(&huart1, "📍 检查引脚状态:\r\n");
    my_printf(&huart1, "  PD0(DC): %d\r\n", HAL_GPIO_ReadPin(LCD_DC_GPIO_Port, LCD_DC_Pin));
    my_printf(&huart1, "  PD1(BLK): %d\r\n", HAL_GPIO_ReadPin(LCD_BLK_GPIO_Port, LCD_BLK_Pin));
    my_printf(&huart1, "  PD4(RES): %d\r\n", HAL_GPIO_ReadPin(LCD_RES_GPIO_Port, LCD_RES_Pin));
    my_printf(&huart1, "  PB13(SCK): %d\r\n", HAL_GPIO_ReadPin(LCD_SCLK_GPIO_Port, LCD_SCLK_Pin));
    my_printf(&huart1, "  PB15(MOSI): %d\r\n", HAL_GPIO_ReadPin(LCD_MOSI_GPIO_Port, LCD_MOSI_Pin));

    // 2. 测试单个像素写入
    my_printf(&huart1, "🎯 测试单个像素写入...\r\n");
    LCD_Address_Set(0, 0, 0, 0);
    LCD_WR_DATA(RED);
    HAL_Delay(1000);

    LCD_Address_Set(1, 0, 1, 0);
    LCD_WR_DATA(GREEN);
    HAL_Delay(1000);

    LCD_Address_Set(2, 0, 2, 0);
    LCD_WR_DATA(BLUE);
    HAL_Delay(1000);

    // 3. 测试读取ID (如果支持)
    my_printf(&huart1, "🆔 尝试读取LCD ID...\r\n");
    LCD_WR_REG(0x04); // Read Display ID
    // 注意：这里需要MISO引脚才能读取，我们的配置是只写的

    my_printf(&huart1, "✅ 深度诊断完成\r\n");
}

/**
 * @brief 硬件连接验证 - 检查SPI通信是否正常
 */
void tft_HardwareConnectionTest(void)
{
    my_printf(&huart1, "🔌 开始硬件连接验证...\r\n");

    // 1. 测试背光控制 - 这个最容易观察
    my_printf(&huart1, "💡 测试背光控制...\r\n");
    for(int i = 0; i < 10; i++) {
        LCD_BLK_Set();
        my_printf(&huart1, "背光开启\r\n");
        HAL_Delay(500);
        LCD_BLK_Clr();
        my_printf(&huart1, "背光关闭\r\n");
        HAL_Delay(500);
    }
    LCD_BLK_Set(); // 最后保持开启

    // 2. 测试复位引脚
    my_printf(&huart1, "🔄 测试复位引脚...\r\n");
    for(int i = 0; i < 5; i++) {
        LCD_RES_Clr();
        my_printf(&huart1, "复位拉低\r\n");
        HAL_Delay(100);
        LCD_RES_Set();
        my_printf(&huart1, "复位拉高\r\n");
        HAL_Delay(100);
    }

    // 3. 手动发送SPI数据并观察引脚
    my_printf(&huart1, "📡 手动SPI测试...\r\n");
    LCD_DC_Clr(); // 命令模式
    my_printf(&huart1, "DC设为命令模式(应该为低电平)\r\n");

    // 手动发送0x55 (01010101)
    my_printf(&huart1, "发送0x55 (01010101)...\r\n");
    for(int bit = 7; bit >= 0; bit--) {
        LCD_SCLK_Clr();
        HAL_Delay(10);

        if(0x55 & (1 << bit)) {
            LCD_MOSI_Set();
            my_printf(&huart1, "位%d: 高电平\r\n", bit);
        } else {
            LCD_MOSI_Clr();
            my_printf(&huart1, "位%d: 低电平\r\n", bit);
        }

        HAL_Delay(10);
        LCD_SCLK_Set();
        HAL_Delay(10);
    }

    LCD_DC_Set(); // 数据模式
    my_printf(&huart1, "DC设为数据模式(应该为高电平)\r\n");

    my_printf(&huart1, "✅ 硬件连接验证完成\r\n");
    my_printf(&huart1, "请用万用表或示波器检查以上引脚变化\r\n");
}

/**
 * @brief 简化的LCD测试 - 专门用于排查白屏问题
 */
void tft_SimpleTest(void)
{
    my_printf(&huart1, "🧪 开始简化LCD测试...\r\n");

    // 先做硬件连接测试
    tft_HardwareConnectionTest();

    // 1. 只初始化LCD，不做其他测试
    LCD_Init();
    HAL_Delay(1000);

    // 2. 尝试填充一个小的红色方块
    my_printf(&huart1, "🔴 测试小红块 (10x10)...\r\n");
    LCD_Fill(0, 0, 10, 10, RED);
    HAL_Delay(3000);

    // 3. 尝试填充一个小的绿色方块
    my_printf(&huart1, "🟢 测试小绿块 (10x10)...\r\n");
    LCD_Fill(20, 0, 30, 10, GREEN);
    HAL_Delay(3000);

    // 4. 尝试填充一个小的蓝色方块
    my_printf(&huart1, "🔵 测试小蓝块 (10x10)...\r\n");
    LCD_Fill(40, 0, 50, 10, BLUE);
    HAL_Delay(3000);

    // 5. 尝试填充黑色背景
    my_printf(&huart1, "⚫ 测试黑色背景...\r\n");
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    HAL_Delay(3000);

    my_printf(&huart1, "✅ 简化测试完成\r\n");
}

/**
 * @brief 极限测试模式 - 尝试不同的LCD驱动和初始化序列
 */
void tft_ExtremeDiagnose(void)
{
    my_printf(&huart1, "🚀 开始极限测试模式...\r\n");

    // 1. 基础GPIO和背光测试
    LCD_GPIO_Init();
    my_printf(&huart1, "💡 测试背光控制 - 请观察LCD背光是否闪烁...\r\n");
    for(int i = 0; i < 5; i++) {
        LCD_BLK_Set();
        my_printf(&huart1, "背光开启 %d/5\r\n", i+1);
        HAL_Delay(1000);
        LCD_BLK_Clr();
        my_printf(&huart1, "背光关闭 %d/5\r\n", i+1);
        HAL_Delay(1000);
    }
    LCD_BLK_Set();
    my_printf(&huart1, "背光测试完成，背光保持开启\r\n");

    // 2. 尝试ST7735驱动初始化
    my_printf(&huart1, "🔄 尝试ST7735驱动初始化...\r\n");
    tft_TryST7735Init();

    // 3. 尝试ST7789驱动初始化
    my_printf(&huart1, "🔄 尝试ST7789驱动初始化...\r\n");
    tft_TryST7789Init();

    // 4. 尝试ILI9163驱动初始化
    my_printf(&huart1, "🔄 尝试ILI9163驱动初始化...\r\n");
    tft_TryILI9163Init();

    // 5. 最后尝试原始ILI9341
    my_printf(&huart1, "🔄 尝试标准ILI9341驱动初始化...\r\n");
    LCD_Init();

    my_printf(&huart1, "✅ 极限测试完成\r\n");
    my_printf(&huart1, "请观察LCD是否有任何变化\r\n");
}

/**
 * @brief 尝试ST7735驱动初始化
 */
void tft_TryST7735Init(void)
{
    // 硬件复位
    LCD_RES_Set();
    HAL_Delay(10);
    LCD_RES_Clr();
    HAL_Delay(10);
    LCD_RES_Set();
    HAL_Delay(120);

    // ST7735初始化序列
    LCD_WR_REG(0x01); // Software reset
    HAL_Delay(150);

    LCD_WR_REG(0x11); // Sleep out
    HAL_Delay(500);

    LCD_WR_REG(0x3A); // Set color mode
    LCD_WR_DATA8(0x05); // 16bit

    LCD_WR_REG(0x36); // Memory access control
    LCD_WR_DATA8(0x00);

    LCD_WR_REG(0x29); // Display on
    HAL_Delay(100);

    // 测试填充
    LCD_WR_REG(0x2A); // Column address set
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x7F);

    LCD_WR_REG(0x2B); // Row address set
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x9F);

    LCD_WR_REG(0x2C); // Memory write
    for(int i = 0; i < 1000; i++) {
        LCD_WR_DATA(RED);
    }

    my_printf(&huart1, "ST7735测试完成\r\n");
    HAL_Delay(3000);
}

/**
 * @brief 尝试ST7789驱动初始化
 */
void tft_TryST7789Init(void)
{
    // 硬件复位
    LCD_RES_Set();
    HAL_Delay(10);
    LCD_RES_Clr();
    HAL_Delay(10);
    LCD_RES_Set();
    HAL_Delay(120);

    // ST7789初始化序列
    LCD_WR_REG(0x01); // Software reset
    HAL_Delay(150);

    LCD_WR_REG(0x11); // Sleep out
    HAL_Delay(500);

    LCD_WR_REG(0x3A); // Set color mode
    LCD_WR_DATA8(0x55); // 16bit RGB565

    LCD_WR_REG(0x36); // Memory access control
    LCD_WR_DATA8(0x00);

    LCD_WR_REG(0x21); // Display inversion on

    LCD_WR_REG(0x29); // Display on
    HAL_Delay(100);

    // 测试填充
    LCD_Address_Set(0, 0, 50, 50);
    for(int i = 0; i < 2500; i++) {
        LCD_WR_DATA(GREEN);
    }

    my_printf(&huart1, "ST7789测试完成\r\n");
    HAL_Delay(3000);
}

/**
 * @brief 尝试ILI9163驱动初始化
 */
void tft_TryILI9163Init(void)
{
    // 硬件复位
    LCD_RES_Set();
    HAL_Delay(10);
    LCD_RES_Clr();
    HAL_Delay(10);
    LCD_RES_Set();
    HAL_Delay(120);

    // ILI9163初始化序列
    LCD_WR_REG(0x01); // Software reset
    HAL_Delay(500);

    LCD_WR_REG(0x11); // Sleep out
    HAL_Delay(500);

    LCD_WR_REG(0x3A); // Pixel format
    LCD_WR_DATA8(0x05); // 16bit

    LCD_WR_REG(0x36); // Memory access control
    LCD_WR_DATA8(0xC8);

    LCD_WR_REG(0x29); // Display on
    HAL_Delay(100);

    // 测试填充
    LCD_Address_Set(0, 0, 30, 30);
    for(int i = 0; i < 900; i++) {
        LCD_WR_DATA(BLUE);
    }

    my_printf(&huart1, "ILI9163测试完成\r\n");
    HAL_Delay(3000);
}

/**
 * @brief TFT屏幕初始化
 */
void tft_Init(void)
{
    my_printf(&huart1, "🚀 开始TFT初始化...\r\n");

    // 选择测试模式：
    // 1 = 简化测试（推荐用于排查白屏问题）
    // 0 = 完整测试
    // 2 = 深度诊断模式
    // 3 = 极限测试模式（尝试不同的LCD驱动）
    // 4 = 白屏专项诊断模式（新增）
    int test_mode = 4;

    if(test_mode == 1) {
        my_printf(&huart1, "🔧 使用简化测试模式\r\n");
        tft_SimpleTest();
        return;
    }
    else if(test_mode == 2) {
        my_printf(&huart1, "🔬 使用深度诊断模式\r\n");
        tft_DeepDiagnose();
        return;
    }
    else if(test_mode == 3) {
        my_printf(&huart1, "🚀 使用极限测试模式\r\n");
        tft_ExtremeDiagnose();
        return;
    }

    // 完整测试模式
    my_printf(&huart1, "🔧 使用完整测试模式\r\n");

    // 首先进行快速引脚测试
    my_printf(&huart1, "🔍 进行快速引脚测试...\r\n");
    LCD_QuickPinTest();

    // 进行简单SPI测试
    my_printf(&huart1, "🔧 进行简单SPI测试...\r\n");
    LCD_SimpleSPITest();

    // 先执行硬件诊断
    tft_HardwareDiagnose();

    // 初始化LCD
    LCD_Init();

    // 执行基础测试
    tft_BasicTest();

    // 清屏 - 设置为黑色背景，便于观察
    my_printf(&huart1, "🖼️ 设置黑色背景...\r\n");
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);

    // 显示初始化信息 - 使用更大的字体和鲜明的颜色
    my_printf(&huart1, "📝 显示文字信息...\r\n");
    LCD_ShowString(10, 10, (const uint8_t*)"Car Xifeng F4", RED, BLACK, 24, 0);
    LCD_ShowString(10, 40, (const uint8_t*)"LCD Init OK", GREEN, BLACK, 20, 0);
    LCD_ShowString(10, 70, (const uint8_t*)"STM32F407VET6", BLUE, BLACK, 16, 0);

    // 显示屏幕分辨率信息
    LCD_ShowString(10, 100, (const uint8_t*)"LCD_W:", YELLOW, BLACK, 16, 0);
    LCD_ShowIntNum(80, 100, LCD_W, 3, YELLOW, BLACK, 16);
    LCD_ShowString(10, 120, (const uint8_t*)"LCD_H:", YELLOW, BLACK, 16, 0);
    LCD_ShowIntNum(80, 120, LCD_H, 3, YELLOW, BLACK, 16);

    // 显示测试数字
    LCD_ShowString(10, 150, (const uint8_t*)"Test: 0123456789", WHITE, BLACK, 16, 0);
    LCD_ShowString(10, 170, (const uint8_t*)"ABCDEFGHIJKLMNOP", CYAN, BLACK, 16, 0);

    my_printf(&huart1, "✅ TFT LCD initialized successfully\r\n");
    my_printf(&huart1, "📺 Display: %dx%d pixels\r\n", LCD_W, LCD_H);
}

/**
 * @brief 引脚配置检测函数 - 帮助确定正确的硬件连接
 */
void tft_PinConfigTest(void)
{
    my_printf(&huart1, "🔍 开始引脚配置检测...\r\n");
    my_printf(&huart1, "请观察LCD屏幕的变化来确定正确的引脚配置\r\n");

    // 测试原始引脚配置
    my_printf(&huart1, "\r\n=== 测试配置1：原始STM32F407ZG兼容模式 ===\r\n");
    my_printf(&huart1, "如果你的LCD硬件连接是按照原始例程连接的，这个配置应该工作\r\n");
    my_printf(&huart1, "引脚配置：SCL=PB13, SDA=PD5, RES=PD4, DC=PD15, BLK=PD1\r\n");

    // 手动配置原始引脚
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能时钟
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();

    // 配置PB13 (SCL)
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    // 配置PD5 (SDA), PD4 (RES), PD15 (DC), PD1 (BLK)
    GPIO_InitStruct.Pin = GPIO_PIN_5 | GPIO_PIN_4 | GPIO_PIN_15 | GPIO_PIN_1;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    // 测试背光控制
    my_printf(&huart1, "💡 测试背光控制 (PD1)...\r\n");
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_RESET); // 关闭背光
    HAL_Delay(1000);
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_SET);   // 打开背光
    HAL_Delay(1000);

    my_printf(&huart1, "如果背光有变化，说明BLK引脚连接正确\r\n");
    HAL_Delay(2000);
}



/**
 * @brief TFT显示任务 - 定期更新显示内容
 */
void tft_Task(void)
{
    static uint32_t last_update = 0;

    // 每1秒更新一次显示
    if (HAL_GetTick() - last_update >= 1000) {
        last_update = HAL_GetTick();

        // 更新计数器
        display_counter++;
        test_float += 0.11f;

        // 显示动态信息
        tft_DisplaySystemInfo();
    }
}

/**
 * @brief 显示系统信息
 */
void tft_DisplaySystemInfo(void)
{
    // 清除动态显示区域
    LCD_Fill(10, 200, LCD_W - 10, 310, BLACK);

    // 显示运行时间
    LCD_ShowString(10, 200, (const uint8_t*)"Runtime:", BLUE, BLACK, 16, 0);
    LCD_ShowIntNum(90, 200, HAL_GetTick() / 1000, 6, BLUE, BLACK, 16);
    LCD_ShowString(150, 200, (const uint8_t*)"s", BLUE, BLACK, 16, 0);

    // 显示更新计数
    LCD_ShowString(10, 220, (const uint8_t*)"Counter:", GREEN, BLACK, 16, 0);
    LCD_ShowIntNum(90, 220, display_counter, 5, GREEN, BLACK, 16);

    // 显示浮点数
    LCD_ShowString(10, 240, (const uint8_t*)"Float:", RED, BLACK, 16, 0);
    LCD_ShowFloatNum1(70, 240, test_float, 4, RED, BLACK, 16);

    // 显示状态指示
    LCD_ShowString(10, 260, (const uint8_t*)"Status: RUNNING", MAGENTA, BLACK, 16, 0);

    // 绘制一些图形元素
    LCD_DrawRectangle(10, 280, 100, 310, BLUE);
    LCD_Fill(15, 285, 95, 305, LIGHTBLUE);

    // 绘制圆形
    Draw_Circle(150, 295, 15, RED);

    // 显示简单的进度条效果
    uint8_t progress = (display_counter % 20) * 10;  // 0-190像素
    LCD_Fill(10, 315, 10 + progress, 318, GREEN);
    LCD_Fill(10 + progress, 315, 200, 318, GRAY);
}



