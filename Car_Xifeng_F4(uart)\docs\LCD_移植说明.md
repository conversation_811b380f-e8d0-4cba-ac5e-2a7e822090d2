# LCD屏幕移植说明

## 概述
本文档说明了如何将2.8寸LCD显示屏从STM32F407ZG例程移植到STM32F407VET6工程中。

## 硬件连接

### LCD屏幕引脚连接 (STM32F407VET6)
| LCD引脚 | STM32F407VET6引脚 | 功能说明 |
|---------|------------------|----------|
| GND     | GND              | 电源地   |
| VCC     | 3.3V/5V          | 电源     |
| SCL     | PB13 (SPI2_SCK)  | SPI时钟线 |
| SDA     | PB15 (SPI2_MOSI) | SPI数据线 |
| RES     | PD4              | 复位引脚 |
| DC      | PD15             | 数据/命令选择 |
| BLK     | PD1              | 背光控制 |

### 注意事项
1. 原例程使用的是STM32F407ZG，引脚配置为：
   - SCL: PG12
   - SDA: PD5
   - RES: PD4
   - DC: PD15
   - BLK: PD1

2. 移植到STM32F407VET6时，我们改用了SPI2接口：
   - SCL: PB13 (SPI2_SCK)
   - SDA: PB15 (SPI2_MOSI)
   - 其他控制引脚保持不变

## 软件架构

### 文件结构
```
MDK-ARM/
├── lcd_init_hal.h      # LCD初始化头文件
├── lcd_init_hal.c      # LCD初始化源文件
├── lcd_display_hal.h   # LCD显示功能头文件
├── lcd_display_hal.c   # LCD显示功能源文件
├── tft_app.h          # TFT应用层头文件
└── tft_app.c          # TFT应用层源文件
```

### 主要功能模块

#### 1. LCD初始化模块 (lcd_init_hal.c/h)
- `LCD_GPIO_Init()`: GPIO初始化
- `LCD_Init()`: LCD屏幕初始化
- `LCD_WR_REG()`: 写命令寄存器
- `LCD_WR_DATA()`: 写数据
- `LCD_Address_Set()`: 设置显示区域

#### 2. LCD显示模块 (lcd_display_hal.c/h)
- `LCD_Fill()`: 区域填充
- `LCD_DrawPoint()`: 画点
- `LCD_DrawLine()`: 画线
- `LCD_DrawRectangle()`: 画矩形
- `Draw_Circle()`: 画圆
- `LCD_ShowChar()`: 显示字符
- `LCD_ShowString()`: 显示字符串
- `LCD_ShowIntNum()`: 显示整数
- `LCD_ShowFloatNum1()`: 显示浮点数
- `LCD_ShowChinese()`: 显示中文（简化版）

#### 3. TFT应用层 (tft_app.c/h)
- `tft_Init()`: TFT应用初始化
- `tft_Task()`: TFT显示任务
- `tft_DisplaySystemInfo()`: 显示系统信息

## 移植要点

### 1. HAL库适配
- 原例程使用标准库，移植时改用HAL库
- GPIO操作使用HAL_GPIO_WritePin()
- 延时函数使用HAL_Delay()

### 2. 引脚重新映射
- 根据STM32F407VET6的引脚资源重新分配LCD控制引脚
- 使用SPI2接口替代原来的GPIO模拟SPI

### 3. 字体简化
- 原例程包含完整的字体库，移植时进行了简化
- 字符显示使用简单的矩形框代替复杂字体
- 中文显示使用方块代替

### 4. 调度器集成
- 将TFT任务集成到现有的调度器系统中
- TFT任务每100ms执行一次，更新显示内容

## 使用方法

### 1. 初始化
```c
// 在System_Init()中调用
tft_Init();
```

### 2. 显示文本
```c
LCD_ShowString(x, y, "Hello World", RED, WHITE, 16, 0);
```

### 3. 显示数字
```c
LCD_ShowIntNum(x, y, 123, 3, BLUE, WHITE, 16);
```

### 4. 绘制图形
```c
LCD_DrawRectangle(x1, y1, x2, y2, GREEN);
Draw_Circle(x, y, radius, RED);
```

## 颜色定义
```c
#define WHITE    0xFFFF
#define BLACK    0x0000
#define BLUE     0x001F
#define RED      0xF800
#define GREEN    0x07E0
#define YELLOW   0xFFE0
// 更多颜色定义见 lcd_display_hal.h
```

## 故障排除

### 1. 屏幕无显示
- 检查电源连接
- 检查引脚连接是否正确
- 确认背光引脚(BLK)是否正常

### 2. 显示异常
- 检查SPI时序是否正确
- 确认复位引脚(RES)工作正常
- 检查数据/命令选择引脚(DC)

### 3. 编译错误
- 确保所有头文件已正确包含在MyDefine.h中
- 检查项目文件是否包含所有源文件

## 扩展功能

### 1. 完整字体支持
- 可以添加完整的ASCII字体库
- 支持多种字体大小

### 2. 中文字体
- 添加中文字库支持
- 实现完整的中文显示功能

### 3. 图片显示
- 支持BMP、JPEG等图片格式
- 实现图片缩放和旋转

### 4. 触摸功能
- 如果LCD支持触摸，可以添加触摸检测
- 实现简单的GUI界面

## 性能优化

### 1. DMA传输
- 可以使用DMA加速SPI传输
- 减少CPU占用率

### 2. 双缓冲
- 实现双缓冲机制
- 避免显示闪烁

### 3. 局部刷新
- 只刷新变化的区域
- 提高显示效率
