<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>导航功能测试</title>
</head>
<body>
    <h1>🗺️ 导航功能测试</h1>
    
    <div>
        <h3>测试步骤：</h3>
        <ol>
            <li>确保您的STM32程序已经编译并烧录</li>
            <li>打开串口调试工具，连接到STM32</li>
            <li>在串口中输入命令：<code>wanda</code></li>
            <li>观察串口输出，应该看到导航数据发送过程</li>
            <li>打开高德地图页面：<a href="amap_gps_tracker.html" target="_blank">amap_gps_tracker.html</a></li>
            <li>在地图上应该能看到从当前位置到万达广场的导航路线</li>
        </ol>
    </div>

    <div>
        <h3>预期效果：</h3>
        <ul>
            <li>✅ 串口显示：开始导航到酃湖万达广场</li>
            <li>✅ 串口显示：当前位置和目的地坐标</li>
            <li>✅ 串口显示：发送导航数据到地图</li>
            <li>✅ 高德地图显示：蓝色导航路线</li>
            <li>✅ 高德地图显示：起点和终点标记</li>
            <li>✅ 高德地图显示：导航信息面板</li>
        </ul>
    </div>

    <div>
        <h3>故障排除：</h3>
        <ul>
            <li>如果串口没有反应，检查命令是否正确输入：<code>wanda</code></li>
            <li>如果地图没有显示路线，检查浏览器控制台是否有错误信息</li>
            <li>如果ESP01连接失败，检查WiFi网络和ThingSpeak连接</li>
        </ul>
    </div>

    <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h3>🎯 功能说明：</h3>
        <p><strong>命令：</strong> <code>wanda</code></p>
        <p><strong>功能：</strong> 从当前GPS位置导航到酃湖万达广场</p>
        <p><strong>坐标：</strong> 万达广场 (26.8869°N, 112.6758°E)</p>
        <p><strong>显示：</strong> 高德地图实时路径规划</p>
    </div>
</body>
</html>
