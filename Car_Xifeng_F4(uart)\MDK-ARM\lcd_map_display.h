/**
 * @file lcd_map_display.h
 * @brief LCD地图显示模块头文件
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

#ifndef __LCD_MAP_DISPLAY_H
#define __LCD_MAP_DISPLAY_H

#include "MyDefine.h"

// 地图显示区域定义
#define MAP_AREA_X      10      // 地图区域X起始位置
#define MAP_AREA_Y      50      // 地图区域Y起始位置  
#define MAP_AREA_WIDTH  220     // 地图区域宽度
#define MAP_AREA_HEIGHT 200     // 地图区域高度

// 地图缩放级别
#define MAP_SCALE_CITY      1   // 城市级别 (约10km范围)
#define MAP_SCALE_DISTRICT  2   // 区域级别 (约5km范围)
#define MAP_SCALE_STREET    3   // 街道级别 (约1km范围)

// 颜色定义
#define MAP_BG_COLOR        BLACK       // 地图背景色
#define MAP_GRID_COLOR      GRAY        // 网格线颜色
#define MAP_CURRENT_COLOR   RED         // 当前位置颜色
#define MAP_DEST_COLOR      GREEN       // 目的地颜色
#define MAP_ROUTE_COLOR     BLUE        // 路径颜色
#define MAP_TEXT_COLOR      WHITE       // 文字颜色

// GPS坐标结构
typedef struct {
    float latitude;     // 纬度
    float longitude;    // 经度
    uint8_t valid;      // 坐标是否有效
} GPS_Coordinate_t;

// 屏幕坐标结构
typedef struct {
    uint16_t x;
    uint16_t y;
} Screen_Point_t;

// 地图状态结构
typedef struct {
    GPS_Coordinate_t current_pos;       // 当前位置
    GPS_Coordinate_t destination;       // 目的地
    GPS_Coordinate_t map_center;        // 地图中心点
    uint8_t scale_level;                // 缩放级别
    uint8_t show_route;                 // 是否显示路径
    uint8_t navigation_active;          // 导航是否激活
    float bearing;                      // 当前方向角度
    float distance_to_dest;             // 到目的地距离(米)
} Map_State_t;

// 导航指令类型
typedef enum {
    NAV_STRAIGHT = 0,   // 直行
    NAV_LEFT,           // 左转
    NAV_RIGHT,          // 右转
    NAV_UTURN,          // 掉头
    NAV_ARRIVE          // 到达
} Navigation_Direction_t;

// 导航指令结构
typedef struct {
    Navigation_Direction_t direction;
    uint16_t distance;      // 距离(米)
    char instruction[32];   // 指令文字
} Navigation_Instruction_t;

// 函数声明
void LCD_Map_Init(void);
void LCD_Map_Clear(void);
void LCD_Map_DrawGrid(void);
void LCD_Map_UpdatePosition(float lat, float lon);
void LCD_Map_SetDestination(float lat, float lon, const char* name);
void LCD_Map_DrawCurrentPosition(void);
void LCD_Map_DrawDestination(void);
void LCD_Map_DrawRoute(void);
void LCD_Map_ShowNavigationInfo(Navigation_Instruction_t* instruction);
void LCD_Map_ShowCompass(float bearing);
void LCD_Map_SetScale(uint8_t scale_level);
Screen_Point_t LCD_Map_GPS_To_Screen(GPS_Coordinate_t gps);
GPS_Coordinate_t LCD_Map_Screen_To_GPS(Screen_Point_t screen);
float LCD_Map_CalculateDistance(GPS_Coordinate_t pos1, GPS_Coordinate_t pos2);
float LCD_Map_CalculateBearing(GPS_Coordinate_t from, GPS_Coordinate_t to);
void LCD_Map_Task(void);

// 全局变量声明
extern Map_State_t g_map_state;

#endif
