#ifndef   __TFT_APP_H
#define  __TFT_APP_H

#include "MyDefine.h"

// LCD初始化和应用层函数
void tft_Init(void);
void tft_Task(void);
void tft_DisplaySystemInfo(void);
void tft_BasicTest(void);  // 添加基础测试函数声明
void tft_HardwareDiagnose(void);  // 添加硬件诊断函数
void tft_DeepDiagnose(void);  // 添加深度诊断函数
void tft_SimpleTest(void);  // 添加简化测试函数
void tft_ExtremeDiagnose(void);  // 添加极限测试函数
void tft_TryST7735Init(void);  // ST7735驱动测试
void tft_TryST7789Init(void);  // ST7789驱动测试
void tft_TryILI9163Init(void);  // ILI9163驱动测试
void tft_PinConfigTest(void);  // 引脚配置检测函数
void LCD_QuickPinTest(void);   // LCD快速引脚测试
void LCD_SimpleSPITest(void);  // 简单SPI通信测试

// LCD驱动相关头文件包含
#include "lcd_init_hal.h"
#include "lcd_display_hal.h"

#endif


