# LCD白屏问题调试指南 - 最新版

## 🚨 当前状态
LCD显示白屏，无任何其他颜色显示。

## 🔧 最新修复 (2025-08-08)

### 1. 引脚冲突修复 ✅
- **问题**: PD15与AF_KEY冲突
- **解决**: 改用PD0作为DC引脚
- **状态**: 已修复

### 2. SPI时序优化 ✅
- **问题**: 原延时太短，168MHz主频需要更多延时
- **解决**: 将延时从10个NOP增加到20个NOP
- **状态**: 已优化

### 3. 添加简化测试模式 ✅
- **功能**: 专门用于排查白屏问题的简化测试
- **特点**: 逐步测试小色块，便于观察
- **状态**: 已添加

### 4. 增强调试功能 ✅
- 深度诊断功能
- 详细的串口输出
- 引脚状态检查

## 硬件连接检查

确认以下引脚连接正确：
- SCLK (PB13) → LCD SCK
- MOSI (PB15) → LCD SDA  
- DC (PD15) → LCD DC
- RES (PD4) → LCD RES
- BLK (PD1) → LCD BL
- VCC → 3.3V
- GND → GND

## 测试步骤

1. **重新编译并烧录代码**
2. **监控串口输出** (115200波特率)
3. **观察初始化调试信息**
4. **发送命令**: `lcd_test`
5. **观察屏幕颜色变化**:
   - 红色 → 绿色 → 蓝色 → 黑色 → 白色

## 调试命令

- `lcd_test` - 执行LCD基础颜色测试
- `help` - 显示所有可用命令

## 常见问题排查

### 如果仍显示白屏:
1. 检查电源电压是否为3.3V
2. 检查引脚连接是否牢固
3. 确认LCD型号是否为ILI9341
4. 检查串口调试输出

### 如果显示花屏:
1. 检查SPI时序设置
2. 确认引脚定义正确
3. 检查电源稳定性

### 如果背光不亮:
1. 检查BLK引脚连接
2. 确认背光控制逻辑
3. 测量背光电压

## 预期的串口输出

正常初始化应该看到：
```
🔧 开始LCD初始化...
✅ LCD GPIO初始化完成
🔄 执行LCD硬件复位...
💡 打开LCD背光...
📋 开始LCD寄存器配置...
😴 退出睡眠模式...
⚙️ 配置电源控制寄存器...
🖼️ 配置显示方向 (USE_HORIZONTAL=0)...
🎨 设置像素格式为RGB565...
🌟 开启LCD显示...
✅ LCD初始化完成！分辨率: 240x320
```

## 联系支持

如果问题仍然存在，请提供：
1. 完整的串口调试输出
2. LCD模块型号和规格
3. 硬件连接照片
4. 万用表测量的引脚电压
