<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径显示测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #333;
            margin-bottom: 20px;
        }
        .controls {
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-info { color: #007bff; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warn { color: #ffc107; }
    </style>
</head>
<body>
    <h1>🗺️ GPS路径显示测试</h1>
    
    <div class="controls">
        <button class="btn" onclick="testWandaRoute()">🛍️ 测试万达路径</button>
        <button class="btn" onclick="testSportsRoute()">🏟️ 测试体育中心路径</button>
        <button class="btn" onclick="testDirectRoute()">📍 测试直线路径</button>
        <button class="btn" onclick="clearMap()">🧹 清除地图</button>
        <button class="btn" onclick="toggleDebug()">🔧 调试模式</button>
    </div>

    <div id="map"></div>
    
    <h3>📋 日志输出</h3>
    <div id="log" class="log"></div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let routeLayer = null;
        let debugMode = false;

        // 坐标定义
        const SCHOOL_LAT = 26.8812;
        const SCHOOL_LON = 112.6769;
        const WANDA_LAT = 26.8892785;
        const WANDA_LON = 112.6609182;
        const SPORTS_LAT = 26.8900796;
        const SPORTS_LON = 112.6741752;

        // 初始化地图
        function initMap() {
            map = L.map('map').setView([SCHOOL_LAT, SCHOOL_LON], 15);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // 添加学校标记
            L.marker([SCHOOL_LAT, SCHOOL_LON])
                .addTo(map)
                .bindPopup('🏫 衡阳师范学院')
                .openPopup();

            log('✅ 地图初始化完成', 'success');
        }

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-${type}`;
            logEntry.textContent = `[${time}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 测试万达路径
        async function testWandaRoute() {
            log('🛍️ 开始测试万达广场路径...', 'info');
            const routeData = await planRouteWithOSRM(SCHOOL_LAT, SCHOOL_LON, WANDA_LAT, WANDA_LON);
            if (routeData) {
                displayRoute(routeData, '万达广场');
            }
        }

        // 测试体育中心路径
        async function testSportsRoute() {
            log('🏟️ 开始测试体育中心路径...', 'info');
            const routeData = await planRouteWithOSRM(SCHOOL_LAT, SCHOOL_LON, SPORTS_LAT, SPORTS_LON);
            if (routeData) {
                displayRoute(routeData, '体育中心');
            }
        }

        // 测试直线路径
        function testDirectRoute() {
            log('📍 创建直线路径测试...', 'info');
            const routeData = {
                coordinates: [
                    [SCHOOL_LON, SCHOOL_LAT],
                    [WANDA_LON, WANDA_LAT]
                ],
                distance: 5000,
                duration: 600,
                provider: 'Direct'
            };
            displayRoute(routeData, '直线路径');
        }

        // OSRM路径规划
        async function planRouteWithOSRM(startLat, startLon, endLat, endLon) {
            try {
                const url = `https://router.project-osrm.org/route/v1/driving/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true`;
                
                log(`🌐 OSRM请求: ${url}`, 'info');
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (debugMode) {
                    log(`📊 OSRM响应: ${JSON.stringify(data, null, 2)}`, 'info');
                }
                
                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0];
                    log(`✅ 路径规划成功: ${route.geometry.coordinates.length}个坐标点`, 'success');
                    log(`📏 距离: ${(route.distance/1000).toFixed(2)}km`, 'success');
                    
                    return {
                        coordinates: route.geometry.coordinates,
                        distance: route.distance,
                        duration: route.duration,
                        provider: 'OSRM'
                    };
                } else {
                    log(`❌ OSRM错误: ${data.message || data.code}`, 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 显示路径
        function displayRoute(routeData, name) {
            if (routeLayer) {
                map.removeLayer(routeLayer);
            }

            if (!routeData || !routeData.coordinates) {
                log('❌ 路径数据无效', 'error');
                return;
            }

            log(`📍 显示${name}路径: ${routeData.coordinates.length}个点`, 'info');
            
            // 转换坐标格式
            const latLngs = routeData.coordinates.map(coord => {
                if (debugMode) {
                    log(`坐标转换: [${coord[0]}, ${coord[1]}] → [${coord[1]}, ${coord[0]}]`, 'info');
                }
                return [coord[1], coord[0]];
            });

            // 创建路径
            routeLayer = L.polyline(latLngs, {
                color: '#FF4444',
                weight: 6,
                opacity: 0.8
            }).addTo(map);

            // 添加终点标记
            const endPoint = latLngs[latLngs.length - 1];
            L.marker(endPoint)
                .addTo(map)
                .bindPopup(`🎯 ${name}`);

            // 调整视野
            map.fitBounds(routeLayer.getBounds(), { padding: [20, 20] });
            
            log(`✅ ${name}路径显示完成`, 'success');
        }

        // 清除地图
        function clearMap() {
            if (routeLayer) {
                map.removeLayer(routeLayer);
                routeLayer = null;
            }
            
            // 清除除学校外的所有标记
            map.eachLayer(layer => {
                if (layer instanceof L.Marker && layer.getPopup() && 
                    !layer.getPopup().getContent().includes('衡阳师范学院')) {
                    map.removeLayer(layer);
                }
            });
            
            log('🧹 地图已清除', 'info');
        }

        // 切换调试模式
        function toggleDebug() {
            debugMode = !debugMode;
            log(`🔧 调试模式: ${debugMode ? '开启' : '关闭'}`, debugMode ? 'info' : 'success');
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initMap();
            log('🚀 路径显示测试页面已加载', 'success');
        };
    </script>
</body>
</html>
