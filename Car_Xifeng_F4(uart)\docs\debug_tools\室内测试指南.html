<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 室内测试指南</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .problem-section {
            background: rgba(255,100,100,0.2);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #ff4444;
        }
        .solution-section {
            background: rgba(100,255,100,0.2);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #00ff88;
        }
        .info-section {
            background: rgba(100,150,255,0.2);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #00bfff;
        }
        .code-block {
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }
        .highlight {
            background: rgba(255,255,0,0.3);
            padding: 2px 4px;
            border-radius: 3px;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding-left: 30px;
            position: relative;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #00ff88;
            color: black;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8em;
        }
        .status-good { color: #00ff88; }
        .status-warning { color: #ffaa00; }
        .status-error { color: #ff4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 室内测试指南</h1>
        
        <div class="info-section">
            <h3>📊 当前状态分析</h3>
            <p>根据你的串口输出，我看到：</p>
            <ul>
                <li class="status-good">✅ ESP01 WiFi连接正常</li>
                <li class="status-good">✅ nav_test3命令被正确接收</li>
                <li class="status-good">✅ 目的地识别正确：tiyuzhongxin</li>
                <li class="status-good">✅ GPS坐标固定：26.881226°N, 112.676903°E</li>
                <li class="status-warning">⚠️ field3=3333 应该被检测到</li>
                <li class="status-error">❌ 网页检测器没有反应</li>
            </ul>
        </div>

        <div class="problem-section">
            <h3>🔍 问题分析</h3>
            
            <h4>1. ThingSpeak更新限制</h4>
            <p>ThingSpeak免费版有<strong>15秒更新间隔限制</strong>，如果你连续发送命令，后面的可能被忽略。</p>
            
            <h4>2. Entry ID重复</h4>
            <p>网页检测器使用Entry ID来避免重复处理，如果ThingSpeak返回相同的Entry ID，会被跳过。</p>
            
            <h4>3. 导航状态冲突</h4>
            <p>如果前一个导航还在进行中，新的导航可能无法启动。</p>
        </div>

        <div class="solution-section">
            <h3>🛠️ 解决方案</h3>
            
            <h4>我已经做的修复：</h4>
            <ul>
                <li>✅ 添加了导航状态清除逻辑</li>
                <li>✅ 在网页检测器中添加了"🔄 重置检测器"按钮</li>
                <li>✅ 增强了调试信息输出</li>
            </ul>
            
            <h4>🧪 标准测试流程：</h4>
            <div class="steps">
                <div class="step">重新编译并烧录修改后的代码</div>
                <div class="step">打开WANDA命令检测器</div>
                <div class="step">点击"🔄 重置检测器"清除缓存</div>
                <div class="step">点击"🚀 开始检测"</div>
                <div class="step">等待15秒确保ThingSpeak准备就绪</div>
                <div class="step">在串口中发送第一个测试命令</div>
                <div class="step">等待15秒再发送下一个命令</div>
            </div>
        </div>

        <div class="info-section">
            <h3>📋 推荐测试序列</h3>
            
            <div class="code-block">
<strong>测试序列（每个命令间隔15秒）：</strong>

1. 发送: <span class="highlight">nav_test1</span>
   期望: field3=1111 → 🛍️ 万达广场
   
2. 等待15秒，发送: <span class="highlight">nav_test2</span>
   期望: field3=2222 → 📚 酃湖书院
   
3. 等待15秒，发送: <span class="highlight">nav_test3</span>
   期望: field3=3333 → 🏟️ 体育中心
   
4. 等待15秒，发送: <span class="highlight">nav_test4</span>
   期望: field3=4444 → 🚄 火车站
   
5. 等待15秒，发送: <span class="highlight">nav_test5</span>
   期望: field3=5555 → 🏥 医院
            </div>
        </div>

        <div class="solution-section">
            <h3>🔧 调试技巧</h3>
            
            <h4>1. 检查ThingSpeak数据</h4>
            <p>直接访问：<a href="https://thingspeak.com/channels/3014831" target="_blank" style="color: #00bfff;">https://thingspeak.com/channels/3014831</a></p>
            <p>确认field3的值是否正确上传</p>
            
            <h4>2. 观察串口调试信息</h4>
            <div class="code-block">
🔍 调试: 接收到的路径数据 = [destination:tiyuzhonxin,waypoints:2,distance:1500]
📝 导航数据: lat=26.881226, lon=112.676903
🎯 目的地: 衡阳市体育中心
🏷️ 命令标识: field3=3333
📤 上传导航数据到ThingSpeak: destination:tiyuzhonxin,waypoints:2,distance:1500
            </div>
            
            <h4>3. 网页检测器调试信息</h4>
            <div class="code-block">
🔍 调试信息: field3=3333 (类型: number)
🔍 destinationKey=3
🔍 Entry ID检查: 当前=12345, 上次=12344
🎉 检测到导航命令: NAV_3_26.881226_112.676903
✅ 导航命令已检测到: 🏟️ 衡阳市体育中心
🆕 这是新的导航命令！
            </div>
        </div>

        <div class="problem-section">
            <h3>⚠️ 常见问题排查</h3>
            
            <h4>如果网页检测器仍然没反应：</h4>
            <ol>
                <li><strong>检查网络</strong>：确保电脑能访问ThingSpeak</li>
                <li><strong>清除浏览器缓存</strong>：Ctrl+F5强制刷新页面</li>
                <li><strong>检查API密钥</strong>：确认READ_API_KEY正确</li>
                <li><strong>手动检查数据</strong>：访问ThingSpeak频道确认数据上传</li>
                <li><strong>重启检测器</strong>：关闭页面重新打开</li>
            </ol>
            
            <h4>如果单片机没有响应：</h4>
            <ol>
                <li><strong>检查串口连接</strong>：确认串口号和波特率</li>
                <li><strong>重启单片机</strong>：按复位按钮</li>
                <li><strong>检查WiFi连接</strong>：确认ESP01连接状态</li>
                <li><strong>检查GPS状态</strong>：确认GPS数据有效</li>
            </ol>
        </div>

        <div class="solution-section">
            <h3>🎯 快速验证方法</h3>
            
            <p><strong>最简单的验证方法：</strong></p>
            <div class="steps">
                <div class="step">发送一个nav_test命令</div>
                <div class="step">立即访问ThingSpeak频道查看field3值</div>
                <div class="step">如果ThingSpeak有数据但网页检测器没反应，说明是检测器问题</div>
                <div class="step">如果ThingSpeak没有数据，说明是单片机上传问题</div>
            </div>
            
            <div style="text-align: center; margin-top: 20px; font-size: 1.2em;">
                <strong>🔧 现在请按照这个流程重新测试！</strong>
            </div>
        </div>
    </div>
</body>
</html>
