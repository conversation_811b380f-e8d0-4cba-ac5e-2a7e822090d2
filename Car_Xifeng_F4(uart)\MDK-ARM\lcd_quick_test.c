/**
 * @file lcd_quick_test.c
 * @brief LCD快速测试程序 - 帮助快速定位LCD白屏问题
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

#include "MyDefine.h"

extern UART_HandleTypeDef huart1;
extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

/**
 * @brief LCD快速引脚测试 - 不依赖复杂的初始化序列
 */
void LCD_QuickPinTest(void)
{
    my_printf(&huart1, "\r\n🚀 LCD快速引脚测试开始...\r\n");
    my_printf(&huart1, "这个测试将帮助你确定LCD的硬件连接是否正确\r\n");
    
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 使能所有可能用到的GPIO时钟
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOE_CLK_ENABLE();
    
    my_printf(&huart1, "\r\n=== 第一步：测试背光控制 ===\r\n");
    
    // 测试可能的背光引脚
    my_printf(&huart1, "测试PD1作为背光控制引脚...\r\n");
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    // 背光闪烁测试
    for(int i = 0; i < 5; i++) {
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_RESET); // 关闭
        my_printf(&huart1, "背光关闭 %d\r\n", i+1);
        HAL_Delay(500);
        HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_SET);   // 打开
        my_printf(&huart1, "背光打开 %d\r\n", i+1);
        HAL_Delay(500);
    }
    
    my_printf(&huart1, "如果LCD背光有闪烁，说明PD1连接正确\r\n");
    my_printf(&huart1, "如果没有变化，可能背光连接到其他引脚\r\n");
    
    // 测试PE8作为背光（原始例程的引脚）
    my_printf(&huart1, "\r\n测试PE8作为背光控制引脚（原始例程）...\r\n");
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
    
    for(int i = 0; i < 3; i++) {
        HAL_GPIO_WritePin(GPIOE, GPIO_PIN_8, GPIO_PIN_RESET);
        my_printf(&huart1, "PE8背光关闭 %d\r\n", i+1);
        HAL_Delay(500);
        HAL_GPIO_WritePin(GPIOE, GPIO_PIN_8, GPIO_PIN_SET);
        my_printf(&huart1, "PE8背光打开 %d\r\n", i+1);
        HAL_Delay(500);
    }
    
    my_printf(&huart1, "\r\n=== 第二步：测试复位引脚 ===\r\n");
    
    // 配置复位引脚PD4
    GPIO_InitStruct.Pin = GPIO_PIN_4;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    my_printf(&huart1, "执行LCD复位序列...\r\n");
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_SET);   // 拉高
    HAL_Delay(10);
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_RESET); // 复位
    HAL_Delay(120);
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_SET);   // 释放复位
    HAL_Delay(120);
    
    my_printf(&huart1, "复位序列完成\r\n");
    
    my_printf(&huart1, "\r\n=== 第三步：配置所有可能的引脚组合 ===\r\n");
    
    // 配置原始例程的引脚组合
    my_printf(&huart1, "配置原始例程引脚组合：\r\n");
    my_printf(&huart1, "SCL=PB13(替代PG12), SDA=PD5, RES=PD4, DC=PD15, BLK=PD1\r\n");
    
    // SCL - PB13 (替代原来的PG12，因为VET6没有GPIOG)
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    
    // SDA=PD5, RES=PD4, DC=PD15, BLK=PD1
    GPIO_InitStruct.Pin = GPIO_PIN_5 | GPIO_PIN_4 | GPIO_PIN_15 | GPIO_PIN_1;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    
    // 设置初始状态
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_SET);  // SCL高
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_5, GPIO_PIN_SET);   // SDA高
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_SET);   // RES高
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_SET);  // DC高
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_1, GPIO_PIN_SET);   // BLK高（背光开）
    
    my_printf(&huart1, "原始引脚配置完成\r\n");
    HAL_Delay(2000);
    
    my_printf(&huart1, "\r\n=== 测试结果分析 ===\r\n");
    my_printf(&huart1, "请观察LCD屏幕的变化：\r\n");
    my_printf(&huart1, "1. 如果背光有闪烁变化，说明背光引脚连接正确\r\n");
    my_printf(&huart1, "2. 如果屏幕从白屏变成其他颜色，说明引脚配置基本正确\r\n");
    my_printf(&huart1, "3. 如果完全没有变化，可能是硬件连接问题\r\n");
    my_printf(&huart1, "\r\n请根据观察结果调整lcd_init_hal.h中的引脚定义\r\n");
}

/**
 * @brief 简单的SPI通信测试
 */
void LCD_SimpleSPITest(void)
{
    my_printf(&huart1, "\r\n🔧 简单SPI通信测试...\r\n");
    
    // 使用当前配置的引脚进行简单的SPI通信测试
    // 发送一些基本命令看LCD是否有响应
    
    // 模拟发送Sleep Out命令
    my_printf(&huart1, "发送Sleep Out命令 (0x11)...\r\n");
    
    // DC低电平 - 命令模式
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_RESET);
    
    // 发送0x11命令
    uint8_t cmd = 0x11;
    for(int i = 7; i >= 0; i--) {
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET); // SCL低
        HAL_Delay(1);
        
        if(cmd & (1 << i)) {
            HAL_GPIO_WritePin(GPIOD, GPIO_PIN_5, GPIO_PIN_SET);   // SDA高
        } else {
            HAL_GPIO_WritePin(GPIOD, GPIO_PIN_5, GPIO_PIN_RESET); // SDA低
        }
        
        HAL_Delay(1);
        HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_SET);   // SCL高
        HAL_Delay(1);
    }
    
    // DC高电平 - 数据模式
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_15, GPIO_PIN_SET);
    
    my_printf(&huart1, "Sleep Out命令发送完成\r\n");
    HAL_Delay(120); // 等待120ms
    
    my_printf(&huart1, "SPI通信测试完成\r\n");
}
