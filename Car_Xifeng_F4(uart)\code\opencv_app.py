"""
OpenCV图像处理应用程序主控制器
提供统一的图像处理应用程序接口
"""
import sys
import os
from typing import Optional, List, Dict, Any
from pathlib import Path

from opencv_config import opencv_config
from image_processor import Logger, ImageLoader, ImageProcessor, ImageDisplayer


class OpenCVApp:
    """OpenCV图像处理应用程序"""
    
    def __init__(self):
        self.logger = Logger()
        self.loader = ImageLoader(self.logger)
        self.processor = ImageProcessor(self.logger)
        self.displayer = ImageDisplayer(self.logger)
        
        self.logger.info("OpenCV应用程序初始化完成")
    
    def process_single_image(self, image_path: str, 
                           grayscale: bool = None,
                           show_analysis: bool = True,
                           save_processed: bool = False,
                           output_path: str = None) -> bool:
        """
        处理单个图像
        
        Args:
            image_path: 图像文件路径
            grayscale: 是否以灰度模式加载
            show_analysis: 是否显示图像分析信息
            save_processed: 是否保存处理后的图像
            output_path: 输出路径（如果保存）
            
        Returns:
            处理是否成功
        """
        self.logger.info(f"开始处理图像: {image_path}")
        
        # 加载图像
        image = self.loader.load_image(image_path, grayscale)
        if image is None:
            return False
        
        # 分析原始图像
        if show_analysis:
            analysis = self.processor.analyze_image(image)
            self._print_image_analysis(analysis, "原始图像")
        
        # 处理图像
        processed_image = self.processor.process_image(image)
        
        # 分析处理后的图像
        if show_analysis and (opencv_config.processing.enable_histogram_equalization or 
                             opencv_config.processing.enable_noise_reduction):
            processed_analysis = self.processor.analyze_image(processed_image)
            self._print_image_analysis(processed_analysis, "处理后图像")
        
        # 显示图像
        success = self.displayer.display_image(processed_image)
        
        # 保存处理后的图像
        if save_processed and success:
            if output_path is None:
                # 生成默认输出路径
                input_path = Path(image_path)
                output_path = str(input_path.parent / f"{input_path.stem}_processed{input_path.suffix}")
            
            self.displayer.save_image(processed_image, output_path)
        
        return success
    
    def process_multiple_images(self, image_paths: List[str],
                              grayscale: bool = None,
                              show_analysis: bool = False,
                              save_processed: bool = False,
                              output_dir: str = None) -> Dict[str, bool]:
        """
        批量处理多个图像
        
        Args:
            image_paths: 图像文件路径列表
            grayscale: 是否以灰度模式加载
            show_analysis: 是否显示图像分析信息
            save_processed: 是否保存处理后的图像
            output_dir: 输出目录
            
        Returns:
            处理结果字典 {路径: 成功状态}
        """
        results = {}
        
        self.logger.info(f"开始批量处理 {len(image_paths)} 个图像")
        
        for i, image_path in enumerate(image_paths, 1):
            self.logger.info(f"处理第 {i}/{len(image_paths)} 个图像: {image_path}")
            
            output_path = None
            if save_processed and output_dir:
                input_path = Path(image_path)
                output_path = str(Path(output_dir) / f"{input_path.stem}_processed{input_path.suffix}")
            
            success = self.process_single_image(
                image_path=image_path,
                grayscale=grayscale,
                show_analysis=show_analysis,
                save_processed=save_processed,
                output_path=output_path
            )
            
            results[image_path] = success
        
        # 统计结果
        successful = sum(results.values())
        self.logger.info(f"批量处理完成: {successful}/{len(image_paths)} 成功")
        
        return results
    
    def scan_directory(self, directory_path: str) -> List[str]:
        """
        扫描目录中的图像文件
        
        Args:
            directory_path: 目录路径
            
        Returns:
            图像文件路径列表
        """
        image_files = []
        
        try:
            directory = Path(directory_path)
            if not directory.exists() or not directory.is_dir():
                self.logger.error(f"目录不存在或不是有效目录: {directory_path}")
                return image_files
            
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    if opencv_config.get_supported_image_path(str(file_path)):
                        image_files.append(str(file_path))
            
            self.logger.info(f"在目录 {directory_path} 中找到 {len(image_files)} 个图像文件")
            
        except Exception as e:
            self.logger.error(f"扫描目录时发生异常: {e}")
        
        return image_files
    
    def _print_image_analysis(self, analysis: Dict[str, Any], title: str = "图像分析"):
        """打印图像分析结果"""
        if 'error' in analysis:
            self.logger.error(f"{title}失败: {analysis['error']}")
            return
        
        print(f"\n=== {title} ===")
        print(f"图像尺寸: {analysis.get('shape', 'N/A')}")
        print(f"数据类型: {analysis.get('dtype', 'N/A')}")
        print(f"图像类型: {analysis.get('image_type', 'N/A')}")
        print(f"通道数: {analysis.get('channels', 'N/A')}")
        print(f"像素总数: {analysis.get('size', 'N/A')}")
        print(f"像素值范围: {analysis.get('min_value', 'N/A')} - {analysis.get('max_value', 'N/A')}")
        print(f"平均值: {analysis.get('mean_value', 'N/A'):.2f}")
        print(f"标准差: {analysis.get('std_value', 'N/A'):.2f}")
        print(f"内存使用: {analysis.get('memory_usage_mb', 'N/A'):.2f} MB")
        print("=" * 30)
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("\n=== OpenCV图像处理应用 ===")
        print("版本: 2.0 (模块化重构版)")
        print("功能: 图像加载、处理、分析和显示")
        print("=" * 40)
        
        while True:
            print("\n请选择操作:")
            print("1. 处理单个图像")
            print("2. 批量处理图像")
            print("3. 扫描目录")
            print("4. 查看配置")
            print("5. 修改配置")
            print("0. 退出")
            
            try:
                choice = input("\n请输入选择 (0-5): ").strip()
                
                if choice == '0':
                    print("感谢使用！")
                    break
                elif choice == '1':
                    self._interactive_single_image()
                elif choice == '2':
                    self._interactive_batch_process()
                elif choice == '3':
                    self._interactive_scan_directory()
                elif choice == '4':
                    self._show_config()
                elif choice == '5':
                    self._modify_config()
                else:
                    print("无效选择，请重试")
                    
            except KeyboardInterrupt:
                print("\n\n用户中断，退出程序")
                break
            except Exception as e:
                print(f"发生错误: {e}")
    
    def _interactive_single_image(self):
        """交互式单图像处理"""
        image_path = input("请输入图像文件路径: ").strip()
        if not image_path:
            print("路径不能为空")
            return
        
        grayscale = input("是否以灰度模式加载? (y/n, 默认根据配置): ").strip().lower()
        grayscale_mode = None if grayscale == '' else grayscale == 'y'
        
        show_analysis = input("是否显示图像分析? (y/n, 默认y): ").strip().lower()
        show_analysis = show_analysis != 'n'
        
        save_processed = input("是否保存处理后的图像? (y/n, 默认n): ").strip().lower()
        save_processed = save_processed == 'y'
        
        self.process_single_image(
            image_path=image_path,
            grayscale=grayscale_mode,
            show_analysis=show_analysis,
            save_processed=save_processed
        )
    
    def _interactive_batch_process(self):
        """交互式批量处理"""
        print("批量处理功能")
        print("请输入图像文件路径，每行一个，输入空行结束:")
        
        image_paths = []
        while True:
            path = input().strip()
            if not path:
                break
            image_paths.append(path)
        
        if not image_paths:
            print("没有输入任何路径")
            return
        
        save_processed = input("是否保存处理后的图像? (y/n, 默认n): ").strip().lower()
        save_processed = save_processed == 'y'
        
        output_dir = None
        if save_processed:
            output_dir = input("请输入输出目录 (默认与原图像同目录): ").strip()
            if output_dir:
                Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        self.process_multiple_images(
            image_paths=image_paths,
            save_processed=save_processed,
            output_dir=output_dir
        )
    
    def _interactive_scan_directory(self):
        """交互式目录扫描"""
        directory_path = input("请输入目录路径: ").strip()
        if not directory_path:
            print("路径不能为空")
            return
        
        image_files = self.scan_directory(directory_path)
        
        if image_files:
            print(f"\n找到的图像文件:")
            for i, file_path in enumerate(image_files, 1):
                print(f"{i}. {file_path}")
        else:
            print("未找到任何图像文件")
    
    def _show_config(self):
        """显示当前配置"""
        print("\n=== 当前配置 ===")
        config_dict = opencv_config.to_dict()
        for section, settings in config_dict.items():
            print(f"\n[{section.upper()}]")
            for key, value in settings.items():
                print(f"  {key}: {value}")
    
    def _modify_config(self):
        """修改配置"""
        print("\n配置修改功能")
        print("注意: 修改将保存到配置文件")
        
        # 这里可以添加配置修改的交互逻辑
        print("配置修改功能待实现...")


def create_app() -> OpenCVApp:
    """创建应用程序实例"""
    return OpenCVApp()


def main():
    """主函数"""
    app = create_app()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 命令行模式
        image_path = sys.argv[1]
        app.process_single_image(image_path)
    else:
        # 交互模式
        app.run_interactive_mode()


if __name__ == "__main__":
    main()
