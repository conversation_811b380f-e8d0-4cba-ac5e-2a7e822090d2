/**
 * 导航路线数据实现
 * 包含完整的从衡阳师范学院到万达广场的导航信息
 */

#include "navigation_routes.h"

// 路线信息
const RouteInfo_t ROUTE_TO_WANDA = {
    .name = "To Wanda Plaza",
    .total_distance = 3.78f,
    .estimated_time = 6,
    .step_count = 15
};

// 完整的导航步骤（从衡阳师范学院到万达广场）
const NavigationStep_t WANDA_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head northeast on Lingtai Rd", "120m", "Lingtai Road", 0, 0},
    {3, "Continue straight", "180m", "Lingtai Road", 0, 0},
    {4, "Approach intersection", "50m", "Lingtai Road", 0, 0},
    {5, "Turn left onto Hengzhou Ave", "0m", "Hengzhou Avenue", 1, 0},
    {6, "Continue on Hengzhou Ave", "300m", "Hengzhou Avenue", 0, 0},
    {7, "Pass Hengyang Normal Univ", "200m", "Hengzhou Avenue", 0, 0},
    {8, "Continue straight", "400m", "Hengzhou Avenue", 0, 0},
    {9, "Pass Zhongshan Road junction", "250m", "Hengzhou Avenue", 0, 0},
    {10, "Continue on Hengzhou Ave", "350m", "Hengzhou Avenue", 0, 0},
    {11, "Pass traffic light", "180m", "Hengzhou Avenue", 0, 0},
    {12, "Continue straight", "320m", "Hengzhou Avenue", 0, 0},
    {13, "Turn right onto Jiangxiang Rd", "0m", "Jiangxiang Road", 2, 0},
    {14, "Continue on Jiangxiang Rd", "119m", "Jiangxiang Road", 0, 0},
    {15, "Arrive at Wanda Plaza", "0m", "Wanda Plaza", 3, 0}
};

/**
 * @brief 加载万达路线
 */
void NavRoutes_LoadWandaRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;
    
    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_WANDA.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_WANDA.estimated_time;
    
    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_WANDA.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = WANDA_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }
    
    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

// 酃湖书院路线信息
const RouteInfo_t ROUTE_TO_ACADEMY = {
    .name = "To Linghu Academy",
    .total_distance = 2.5f,
    .estimated_time = 3,
    .step_count = 8
};

// 酃湖书院路线步骤
const NavigationStep_t ACADEMY_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head toward campus gate", "120m", "Campus Road", 0, 0},
    {3, "Exit campus via East Gate", "180m", "East Gate", 0, 0},
    {4, "Enter Laiyan Road", "50m", "Laiyan Road", 0, 0},
    {5, "Continue southwest on Laiyan Rd", "800m", "Laiyan Road", 0, 0},
    {6, "Continue straight", "600m", "Laiyan Road", 0, 0},
    {7, "Approach Academy area", "200m", "Laiyan Road", 0, 0},
    {8, "Arrive at Linghu Academy", "0m", "Linghu Academy", 3, 0}
};

// 体育中心路线信息
const RouteInfo_t ROUTE_TO_SPORTS = {
    .name = "To Sports Center",
    .total_distance = 4.2f,
    .estimated_time = 5,
    .step_count = 7
};

// 体育中心路线步骤
const NavigationStep_t SPORTS_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head toward campus gate", "120m", "Campus Road", 0, 0},
    {3, "Exit campus via East Gate", "180m", "East Gate", 0, 0},
    {4, "Enter Laiyan Road", "50m", "Laiyan Road", 0, 0},
    {5, "Follow direct route to Sports Center", "1500m", "Direct Route", 0, 0},
    {6, "Continue toward Sports Center", "2000m", "Sports Center Road", 0, 0},
    {7, "Arrive at Sports Center", "0m", "Sports Center", 3, 0}
};

// 火车站路线信息
const RouteInfo_t ROUTE_TO_TRAIN = {
    .name = "To Train Station",
    .total_distance = 5.1f,
    .estimated_time = 6,
    .step_count = 11
};

// 火车站路线步骤
const NavigationStep_t TRAIN_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head toward campus gate", "120m", "Campus Road", 0, 0},
    {3, "Exit campus via East Gate", "180m", "East Gate", 0, 0},
    {4, "Enter Laiyan Road", "50m", "Laiyan Road", 0, 0},
    {5, "Continue west on Laiyan Rd", "800m", "Laiyan Road", 0, 0},
    {6, "Continue on Laiyan Road", "600m", "Laiyan Road", 0, 0},
    {7, "Turn onto Zhengyang North Rd", "0m", "Zhengyang North Road", 1, 0},
    {8, "Continue northwest", "1200m", "Zhengyang North Road", 0, 0},
    {9, "Continue toward station", "800m", "Zhengyang North Road", 0, 0},
    {10, "Approach station area", "600m", "Station Road", 0, 0},
    {11, "Arrive at Train Station", "0m", "Train Station", 3, 0}
};

// 医院路线信息
const RouteInfo_t ROUTE_TO_HOSPITAL = {
    .name = "To Hospital",
    .total_distance = 3.2f,
    .estimated_time = 4,
    .step_count = 12
};

// 医院路线步骤
const NavigationStep_t HOSPITAL_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head toward campus gate", "120m", "Campus Road", 0, 0},
    {3, "Exit campus via East Gate", "180m", "East Gate", 0, 0},
    {4, "Enter Laiyan Road", "50m", "Laiyan Road", 0, 0},
    {5, "Continue west on Laiyan Rd", "500m", "Laiyan Road", 0, 0},
    {6, "Continue northwest", "400m", "Laiyan Road", 0, 0},
    {7, "Turn onto Zhengyang North Rd", "0m", "Zhengyang North Road", 1, 0},
    {8, "Continue northwest", "800m", "Zhengyang North Road", 0, 0},
    {9, "Continue toward hospital", "600m", "Zhengyang North Road", 0, 0},
    {10, "Turn onto Chuanshan Road", "0m", "Chuanshan Road", 2, 0},
    {11, "Continue to hospital", "400m", "Chuanshan Road", 0, 0},
    {12, "Arrive at Hospital", "0m", "Hospital", 3, 0}
};

/**
 * @brief 加载自定义路线
 */
void NavRoutes_LoadCustomRoute(const NavigationStep_t* steps, uint8_t count, float distance, uint16_t time)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息
    g_nav_paging.total_distance = distance;
    g_nav_paging.estimated_time = time;

    // 复制步骤数据
    for (uint8_t i = 0; i < count && i < 20; i++) {
        g_nav_paging.steps[g_nav_paging.total_steps] = steps[i];
        g_nav_paging.total_steps++;
    }

    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 加载酃湖书院路线
 */
void NavRoutes_LoadAcademyRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_ACADEMY.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_ACADEMY.estimated_time;

    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_ACADEMY.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = ACADEMY_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }

    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 加载体育中心路线
 */
void NavRoutes_LoadSportsRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_SPORTS.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_SPORTS.estimated_time;

    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_SPORTS.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = SPORTS_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }

    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 加载火车站路线
 */
void NavRoutes_LoadTrainRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_TRAIN.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_TRAIN.estimated_time;

    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_TRAIN.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = TRAIN_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }

    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 加载医院路线
 */
void NavRoutes_LoadHospitalRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;

    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_HOSPITAL.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_HOSPITAL.estimated_time;

    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_HOSPITAL.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = HOSPITAL_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }

    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}
