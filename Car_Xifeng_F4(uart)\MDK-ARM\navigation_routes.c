/**
 * 导航路线数据实现
 * 包含完整的从衡阳师范学院到万达广场的导航信息
 */

#include "navigation_routes.h"

// 路线信息
const RouteInfo_t ROUTE_TO_WANDA = {
    .name = "To Wanda Plaza",
    .total_distance = 3.78f,
    .estimated_time = 6,
    .step_count = 15
};

// 完整的导航步骤（从衡阳师范学院到万达广场）
const NavigationStep_t WANDA_ROUTE_STEPS[] = {
    {1, "Start from Lingtai Road", "0m", "Lingtai Road", 0, 0},
    {2, "Head northeast on Lingtai Rd", "120m", "Lingtai Road", 0, 0},
    {3, "Continue straight", "180m", "Lingtai Road", 0, 0},
    {4, "Approach intersection", "50m", "Lingtai Road", 0, 0},
    {5, "Turn left onto Hengzhou Ave", "0m", "Hengzhou Avenue", 1, 0},
    {6, "Continue on Hengzhou Ave", "300m", "Hengzhou Avenue", 0, 0},
    {7, "Pass Hengyang Normal Univ", "200m", "Hengzhou Avenue", 0, 0},
    {8, "Continue straight", "400m", "Hengzhou Avenue", 0, 0},
    {9, "Pass Zhongshan Road junction", "250m", "Hengzhou Avenue", 0, 0},
    {10, "Continue on Hengzhou Ave", "350m", "Hengzhou Avenue", 0, 0},
    {11, "Pass traffic light", "180m", "Hengzhou Avenue", 0, 0},
    {12, "Continue straight", "320m", "Hengzhou Avenue", 0, 0},
    {13, "Turn right onto Jiangxiang Rd", "0m", "Jiangxiang Road", 2, 0},
    {14, "Continue on Jiangxiang Rd", "119m", "Jiangxiang Road", 0, 0},
    {15, "Arrive at Wanda Plaza", "0m", "Wanda Plaza", 3, 0}
};

/**
 * @brief 加载万达路线
 */
void NavRoutes_LoadWandaRoute(void)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;
    
    // 设置路线信息
    g_nav_paging.total_distance = ROUTE_TO_WANDA.total_distance;
    g_nav_paging.estimated_time = ROUTE_TO_WANDA.estimated_time;
    
    // 复制步骤数据
    for (uint8_t i = 0; i < ROUTE_TO_WANDA.step_count; i++) {
        if (g_nav_paging.total_steps < 20) {
            g_nav_paging.steps[g_nav_paging.total_steps] = WANDA_ROUTE_STEPS[i];
            g_nav_paging.total_steps++;
        }
    }
    
    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}

/**
 * @brief 加载自定义路线
 */
void NavRoutes_LoadCustomRoute(const NavigationStep_t* steps, uint8_t count, float distance, uint16_t time)
{
    // 清除现有步骤
    g_nav_paging.total_steps = 0;
    
    // 设置路线信息
    g_nav_paging.total_distance = distance;
    g_nav_paging.estimated_time = time;
    
    // 复制步骤数据
    for (uint8_t i = 0; i < count && i < 20; i++) {
        g_nav_paging.steps[g_nav_paging.total_steps] = steps[i];
        g_nav_paging.total_steps++;
    }
    
    // 重新计算页数
    g_nav_paging.total_pages = (g_nav_paging.total_steps + g_nav_paging.steps_per_page - 1) / g_nav_paging.steps_per_page;
    g_nav_paging.current_page = 0;
    g_nav_paging.current_step = 1;
}
