#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "i2c.h"

/* ========== C 语言头文件 ========== */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 核心调度器头文件 ========== */
#include "Scheduler.h"

/* ========== 组件库头文件 ========== */
#include "hardware_iic.h"
#include "ringbuffer.h"

/* ========== 驱动库头文件 ========== */

#include "uart_driver.h"
#include "uart2_driver.h"
#include "uart3_driver.h"
#include "uart6_driver.h"

/* ========== LCD驱动头文件 ========== */
#include "lcd_init_hal.h"
#include "lcd_display_hal.h"
#include "lcd_map_display.h"
#include "lcd_test.h"

/* ========== 导航系统头文件 ========== */
#include "navigation_types.h"
#include "navigation_paging.h"
#include "navigation_routes.h"

/* ========== 应用层头文件 ========== */
#include "uart_app.h"
#include "uart2_app.h"
#include "uart3_app.h"
#include "esp01_app.h"
#include "GPS_app.h"
#include "uart6_app.h"
#include "tft_app.h"
#include "navigation_app.h"

/* ========== 全局用户变量 ========== */



#endif


