#ifndef __LCD_INIT_HAL_H
#define __LCD_INIT_HAL_H

#include "MyDefine.h"

// LCD屏幕方向配置
#define USE_HORIZONTAL 0  // 设置横屏或者竖屏显示 0或1为竖屏 2或3为横屏

#if USE_HORIZONTAL==0||USE_HORIZONTAL==1
#define LCD_W 240
#define LCD_H 320
#else
#define LCD_W 320
#define LCD_H 240
#endif

// LCD GPIO引脚定义 - 适配STM32F407VET6
// 根据您的硬件连接修改这些引脚定义
// 注意：这些引脚需要在gpio.c中进行配置

// SPI时钟线 - 使用PB13 (SPI2_SCK)
#define LCD_SCLK_GPIO_Port    GPIOB
#define LCD_SCLK_Pin          GPIO_PIN_13

// SPI数据线 - 使用PB15 (SPI2_MOSI)  
#define LCD_MOSI_GPIO_Port    GPIOB
#define LCD_MOSI_Pin          GPIO_PIN_15

// 复位引脚 - 使用PD4
#define LCD_RES_GPIO_Port     GPIOD
#define LCD_RES_Pin           GPIO_PIN_4

// 数据/命令选择引脚 - 改用PD0 (避免与AF_KEY冲突)
#define LCD_DC_GPIO_Port      GPIOD
#define LCD_DC_Pin            GPIO_PIN_0

// 背光控制引脚 - 使用PD1
#define LCD_BLK_GPIO_Port     GPIOD
#define LCD_BLK_Pin           GPIO_PIN_1

// LCD控制引脚操作宏定义
#define LCD_SCLK_Clr()    HAL_GPIO_WritePin(LCD_SCLK_GPIO_Port, LCD_SCLK_Pin, GPIO_PIN_RESET)
#define LCD_SCLK_Set()    HAL_GPIO_WritePin(LCD_SCLK_GPIO_Port, LCD_SCLK_Pin, GPIO_PIN_SET)

#define LCD_MOSI_Clr()    HAL_GPIO_WritePin(LCD_MOSI_GPIO_Port, LCD_MOSI_Pin, GPIO_PIN_RESET)
#define LCD_MOSI_Set()    HAL_GPIO_WritePin(LCD_MOSI_GPIO_Port, LCD_MOSI_Pin, GPIO_PIN_SET)

#define LCD_RES_Clr()     HAL_GPIO_WritePin(LCD_RES_GPIO_Port, LCD_RES_Pin, GPIO_PIN_RESET)
#define LCD_RES_Set()     HAL_GPIO_WritePin(LCD_RES_GPIO_Port, LCD_RES_Pin, GPIO_PIN_SET)

#define LCD_DC_Clr()      HAL_GPIO_WritePin(LCD_DC_GPIO_Port, LCD_DC_Pin, GPIO_PIN_RESET)
#define LCD_DC_Set()      HAL_GPIO_WritePin(LCD_DC_GPIO_Port, LCD_DC_Pin, GPIO_PIN_SET)

#define LCD_BLK_Clr()     HAL_GPIO_WritePin(LCD_BLK_GPIO_Port, LCD_BLK_Pin, GPIO_PIN_RESET)
#define LCD_BLK_Set()     HAL_GPIO_WritePin(LCD_BLK_GPIO_Port, LCD_BLK_Pin, GPIO_PIN_SET)

// 函数声明
void LCD_GPIO_Init(void);           // 初始化GPIO
void LCD_Writ_Bus(uint8_t dat);     // 模拟SPI时序
void LCD_WR_DATA8(uint8_t dat);     // 写入一个字节
void LCD_WR_DATA(uint16_t dat);     // 写入两个字节
void LCD_WR_REG(uint8_t dat);       // 写入一个指令
void LCD_Address_Set(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2); // 设置坐标函数
void LCD_Init(void);                // LCD初始化

#endif
