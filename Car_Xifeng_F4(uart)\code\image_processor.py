"""
图像处理模块
提供图像加载、处理、显示和分析功能
"""
import sys
import os
from typing import Optional, Tuple, Dict, Any
import logging
from pathlib import Path

import numpy as np

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("警告: OpenCV未安装，请运行: pip install opencv-python")

from opencv_config import opencv_config


class Logger:
    """日志管理器"""
    
    def __init__(self):
        self._logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        if not opencv_config.logging.enable_logging:
            return
        
        self._logger = logging.getLogger('opencv_app')
        self._logger.setLevel(getattr(logging, opencv_config.logging.log_level.upper()))
        
        # 清除现有处理器
        self._logger.handlers.clear()
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self._logger.addHandler(console_handler)
        
        # 文件处理器
        if opencv_config.logging.log_to_file:
            try:
                file_handler = logging.FileHandler(
                    opencv_config.logging.log_file_path, 
                    encoding='utf-8'
                )
                file_handler.setFormatter(console_formatter)
                self._logger.addHandler(file_handler)
            except Exception as e:
                print(f"无法创建日志文件: {e}")
    
    def info(self, message: str):
        if self._logger:
            self._logger.info(message)
    
    def warning(self, message: str):
        if self._logger:
            self._logger.warning(message)
    
    def error(self, message: str):
        if self._logger:
            self._logger.error(message)
    
    def debug(self, message: str):
        if self._logger:
            self._logger.debug(message)


class ImageLoader:
    """图像加载器"""
    
    def __init__(self, logger: Logger):
        self.logger = logger
    
    def load_image(self, image_path: str, grayscale: bool = None) -> Optional[np.ndarray]:
        """
        加载图像文件
        
        Args:
            image_path: 图像文件路径
            grayscale: 是否以灰度模式加载，None时使用配置默认值
            
        Returns:
            加载的图像数组，失败时返回None
        """
        if not CV2_AVAILABLE:
            self.logger.error("OpenCV不可用，无法加载图像")
            return None
        
        # 检查文件路径
        validated_path = opencv_config.get_supported_image_path(image_path)
        if not validated_path:
            self.logger.error(f"图像文件不存在或格式不支持: {image_path}")
            return None
        
        # 确定加载模式
        if grayscale is None:
            grayscale = opencv_config.image.default_grayscale
        
        try:
            if grayscale:
                image = cv2.imread(validated_path, cv2.IMREAD_GRAYSCALE)
            else:
                image = cv2.imread(validated_path, cv2.IMREAD_COLOR)
            
            if image is None:
                self.logger.error(f"无法读取图像文件: {validated_path}")
                return None
            
            # 检查图像大小
            height, width = image.shape[:2]
            max_width, max_height = opencv_config.image.max_image_size
            
            if width > max_width or height > max_height:
                self.logger.warning(f"图像尺寸过大: {width}x{height}, 最大支持: {max_width}x{max_height}")
                # 可以选择缩放或拒绝加载
                scale = min(max_width / width, max_height / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height))
                self.logger.info(f"图像已缩放至: {new_width}x{new_height}")
            
            self.logger.info(f"成功加载图像: {validated_path}, 尺寸: {image.shape}")
            return image
            
        except Exception as e:
            self.logger.error(f"加载图像时发生异常: {e}")
            return None


class ImageProcessor:
    """图像处理器"""
    
    def __init__(self, logger: Logger):
        self.logger = logger
    
    def process_image(self, image: np.ndarray) -> np.ndarray:
        """
        处理图像
        
        Args:
            image: 输入图像
            
        Returns:
            处理后的图像
        """
        if not CV2_AVAILABLE:
            self.logger.warning("OpenCV不可用，跳过图像处理")
            return image
        
        processed_image = image.copy()
        
        try:
            # 直方图均衡化
            if opencv_config.processing.enable_histogram_equalization:
                if len(processed_image.shape) == 2:  # 灰度图
                    processed_image = cv2.equalizeHist(processed_image)
                else:  # 彩色图
                    # 转换到YUV色彩空间，只对Y通道进行均衡化
                    yuv = cv2.cvtColor(processed_image, cv2.COLOR_BGR2YUV)
                    yuv[:,:,0] = cv2.equalizeHist(yuv[:,:,0])
                    processed_image = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)
                self.logger.debug("应用直方图均衡化")
            
            # 噪声减少
            if opencv_config.processing.enable_noise_reduction:
                kernel_size = opencv_config.processing.gaussian_blur_kernel
                sigma = opencv_config.processing.gaussian_blur_sigma
                processed_image = cv2.GaussianBlur(processed_image, kernel_size, sigma)
                self.logger.debug(f"应用高斯模糊: 核大小={kernel_size}, sigma={sigma}")
            
            return processed_image
            
        except Exception as e:
            self.logger.error(f"图像处理时发生异常: {e}")
            return image
    
    def analyze_image(self, image: np.ndarray) -> Dict[str, Any]:
        """
        分析图像属性
        
        Args:
            image: 输入图像
            
        Returns:
            图像分析结果字典
        """
        analysis = {}
        
        try:
            # 基本信息
            analysis['shape'] = image.shape
            analysis['dtype'] = str(image.dtype)
            analysis['size'] = image.size
            
            # 像素值统计
            analysis['min_value'] = float(np.min(image))
            analysis['max_value'] = float(np.max(image))
            analysis['mean_value'] = float(np.mean(image))
            analysis['std_value'] = float(np.std(image))
            
            # 图像类型
            if len(image.shape) == 2:
                analysis['image_type'] = 'grayscale'
                analysis['channels'] = 1
            elif len(image.shape) == 3:
                analysis['image_type'] = 'color'
                analysis['channels'] = image.shape[2]
            else:
                analysis['image_type'] = 'unknown'
                analysis['channels'] = 0
            
            # 内存使用
            analysis['memory_usage_mb'] = image.nbytes / (1024 * 1024)
            
            self.logger.debug(f"图像分析完成: {analysis}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"图像分析时发生异常: {e}")
            return {'error': str(e)}


class ImageDisplayer:
    """图像显示器"""
    
    def __init__(self, logger: Logger):
        self.logger = logger
    
    def display_image(self, image: np.ndarray, window_name: str = None) -> bool:
        """
        显示图像
        
        Args:
            image: 要显示的图像
            window_name: 窗口名称
            
        Returns:
            显示是否成功
        """
        if not CV2_AVAILABLE:
            self.logger.error("OpenCV不可用，无法显示图像")
            return False
        
        if window_name is None:
            window_name = opencv_config.display.window_name
        
        try:
            # 计算显示大小
            display_size = opencv_config.get_display_size(image.shape)
            
            # 如果需要缩放
            if display_size != image.shape[:2]:
                display_image = cv2.resize(image, (display_size[1], display_size[0]))
                self.logger.debug(f"图像缩放用于显示: {image.shape[:2]} -> {display_size}")
            else:
                display_image = image
            
            # 创建窗口
            cv2.namedWindow(window_name, opencv_config.display.window_flags)
            
            # 显示图像
            cv2.imshow(window_name, display_image)
            self.logger.info(f"显示图像: {window_name}")
            
            # 等待按键
            self.logger.info("按任意键关闭窗口...")
            cv2.waitKey(opencv_config.display.wait_key_timeout)
            
            # 清理
            cv2.destroyAllWindows()
            self.logger.info("窗口已关闭")
            
            return True
            
        except Exception as e:
            self.logger.error(f"显示图像时发生异常: {e}")
            return False
    
    def save_image(self, image: np.ndarray, output_path: str) -> bool:
        """
        保存图像
        
        Args:
            image: 要保存的图像
            output_path: 输出路径
            
        Returns:
            保存是否成功
        """
        if not CV2_AVAILABLE:
            self.logger.error("OpenCV不可用，无法保存图像")
            return False
        
        try:
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存图像
            success = cv2.imwrite(output_path, image)
            
            if success:
                self.logger.info(f"图像已保存: {output_path}")
                return True
            else:
                self.logger.error(f"保存图像失败: {output_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"保存图像时发生异常: {e}")
            return False
