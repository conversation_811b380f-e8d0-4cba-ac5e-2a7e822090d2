#include "navigation_app.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

Navigation_t current_navigation = {0};
NavigationState_t nav_state = NAV_STATE_IDLE;

static const Destination_t destinations[MAX_DESTINATIONS] = {
    {"wangda", 26.8892785f, 112.6609182f, "酃湖万达广场"},
    {"wanda", 26.8892785f, 112.6609182f, "酃湖万达广场"},  // 添加wanda别名
    {"shuyuan", 26.8850f, 112.6700f, "酃湖书院"},  // 新增酃湖书院
    {"tiyuzhonxin", 26.8900796f, 112.6741752f, "衡阳市体育中心"},  // 修正体育中心坐标
    {"gaotie", 26.8945f, 112.6123f, "衡阳东高铁站"},
    {"daxue", 26.8812f, 112.6769f, "衡阳师范学院"},
    {"yiyuan", 26.9043654f, 112.5962734f, "南华大学附属第一医院"},  // 更新医院坐标
    {"gongyuan", 26.8934f, 112.5967f, "石鼓公园"},
    {"shangchang", 26.8823f, 112.6145f, "步步高购物中心"},
    {"jichang", 26.7345f, 112.6789f, "衡阳南岳机场"},
    {"xuexiao", 26.8812f, 112.6769f, "衡阳师范学院(校区)"},
    {"zhongxin", 26.8912f, 112.6034f, "衡阳市中心"},
    {"huochezhan", 26.8934986f, 112.6260051f, "衡阳火车站"}  // 更新火车站坐标
};

void Navigation_Init(void)
{
    nav_state = NAV_STATE_IDLE;
    memset(&current_navigation, 0, sizeof(Navigation_t));
    my_printf(&huart1, "导航系统已初始化，输入 nav_help 查看帮助\r\n");
}

void Navigation_Task(void)
{
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();
    
    if (current_time - last_update < 1000) return;
    last_update = current_time;
    
    switch (nav_state) {
        case NAV_STATE_NAVIGATING:
            Navigation_UpdateProgress();
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "已到达目的地: %s\r\n", current_navigation.destination.description);
            nav_state = NAV_STATE_IDLE;
            break;
        default:
            break;
    }
}

uint8_t Navigation_StartNavigation(const char* destination_name)
{
    // 先停止当前导航（如果有的话）
    if (nav_state != NAV_STATE_IDLE) {
        my_printf(&huart1, "⏹️ 停止当前导航，开始新导航\r\n");
        Navigation_StopNavigation();
        HAL_Delay(100); // 短暂延迟确保状态清除
    }

    // 在串口6和串口1中显示调试信息
    my_printf(&huart6, "🔍 正在查找目的地: %s\r\n", destination_name);
    my_printf(&huart1, "🔍 正在查找目的地: %s\r\n", destination_name);

    Destination_t dest;
    if (!Navigation_FindDestination(destination_name, &dest)) {
        my_printf(&huart6, "❌ 未找到目的地: %s\r\n", destination_name);
        my_printf(&huart1, "❌ 未找到目的地: %s\r\n", destination_name);
        return 0;
    }

    my_printf(&huart6, "✅ 找到目的地: %s\r\n", dest.description);
    my_printf(&huart1, "✅ 找到目的地: %s\r\n", dest.description);

    // 使用ESP01获取GPS数据 - 和WANDA命令一样的方法
    extern void esp01_GetRealLocation(float *lat, float *lon, float *alt);
    float current_lat, current_lon, current_alt;
    esp01_GetRealLocation(&current_lat, &current_lon, &current_alt);

    my_printf(&huart6, "📍 当前GPS坐标: %.6f°N, %.6f°E\r\n", current_lat, current_lon);
    my_printf(&huart1, "📍 当前GPS坐标: %.6f°N, %.6f°E\r\n", current_lat, current_lon);

    if (current_lat == 0.0f || current_lon == 0.0f) {
        my_printf(&huart6, "❌ GPS信号无效，无法开始导航\r\n");
        my_printf(&huart6, "💡 请等待GPS信号稳定后重试\r\n");
        my_printf(&huart1, "GPS信号无效，无法开始导航\r\n");
        return 0;
    }

    my_printf(&huart6, "✅ GPS信号有效，开始路径规划...\r\n");
    
    current_navigation.destination = dest;
    current_navigation.current_waypoint = 0;
    current_navigation.is_active = 1;
    current_navigation.is_arrived = 0;

    my_printf(&huart6, "🛣️ 开始详细路径规划...\r\n");
    Navigation_PlanRoute(current_lat, current_lon, dest.latitude, dest.longitude);

    nav_state = NAV_STATE_NAVIGATING;

    my_printf(&huart6, "🎯 导航启动成功!\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米\r\n", current_navigation.total_distance);

    my_printf(&huart1, "开始导航到: %s\r\n", dest.description);
    my_printf(&huart1, "总距离: %.0f米\r\n", current_navigation.total_distance);

    // 简单直接上传导航命令到ThingSpeak - 仿照WANDA命令的方法
    extern void esp01_UploadNavigationCommand(int field3_value);

    // 根据目的地确定field3值
    int field3_value = 9999; // 默认万达
    if (strcmp(dest.name, "wangda") == 0 || strcmp(dest.name, "wanda") == 0) {
        field3_value = 1111; // 万达广场
    } else if (strcmp(dest.name, "shuyuan") == 0) {
        field3_value = 2222; // 酃湖书院
    } else if (strcmp(dest.name, "tiyuzhonxin") == 0) {
        field3_value = 3333; // 体育中心
    } else if (strcmp(dest.name, "huochezhan") == 0) {
        field3_value = 4444; // 火车站
    } else if (strcmp(dest.name, "yiyuan") == 0) {
        field3_value = 5555; // 医院
    }

    my_printf(&huart1, "📤 直接上传导航命令: field3=%d (%s)\r\n", field3_value, dest.description);

    // 直接上传导航命令
    esp01_UploadNavigationCommand(field3_value);

    return 1;
}

void Navigation_StopNavigation(void)
{
    nav_state = NAV_STATE_IDLE;
    current_navigation.is_active = 0;
    my_printf(&huart1, "������ֹͣ\r\n");
}

void Navigation_ProcessCommand(const char* command)
{
    if (strncmp(command, "nav_", 4) == 0) {
        const char* nav_cmd = command + 4;
        
        if (strcmp(nav_cmd, "help") == 0) {
            my_printf(&huart1, "����ϵͳ����:\r\n");
            my_printf(&huart1, "nav_help - ��ʾ����\r\n");
            my_printf(&huart1, "nav_list - ��ʾĿ�ĵ��б�\r\n");
            my_printf(&huart1, "nav_status - ��ʾ����״̬\r\n");
            my_printf(&huart1, "nav_stop - ֹͣ����\r\n");
            my_printf(&huart1, "wangda - ������۹�����\r\n");
            my_printf(&huart1, "gaotie - ����������վ\r\n");
        }
        else if (strcmp(nav_cmd, "list") == 0) {
            Navigation_PrintDestinations();
        }
        else if (strcmp(nav_cmd, "status") == 0) {
            Navigation_PrintStatus();
        }
        else if (strcmp(nav_cmd, "stop") == 0) {
            Navigation_StopNavigation();
        }
        else if (strcmp(nav_cmd, "test1") == 0) {
            my_printf(&huart1, "测试万达广场导航 (field3=1111)\r\n");
            Navigation_StartNavigation("wangda");
        }
        else if (strcmp(nav_cmd, "test2") == 0) {
            my_printf(&huart1, "测试酃湖书院导航 (field3=2222)\r\n");
            Navigation_StartNavigation("shuyuan");
        }
        else if (strcmp(nav_cmd, "test3") == 0) {
            my_printf(&huart1, "测试体育中心导航 (field3=3333)\r\n");
            Navigation_StartNavigation("tiyuzhonxin");
        }
        else if (strcmp(nav_cmd, "test4") == 0) {
            my_printf(&huart1, "测试火车站导航 (field3=4444)\r\n");
            Navigation_StartNavigation("huochezhan");
        }
        else if (strcmp(nav_cmd, "test5") == 0) {
            my_printf(&huart1, "测试医院导航 (field3=5555)\r\n");
            Navigation_StartNavigation("yiyuan");
        }
    }
    else {
        // ����Ƿ�ΪĿ�ĵ�����
        uint8_t found = 0;
        for (int i = 0; i < MAX_DESTINATIONS; i++) {
            if (strcmp(command, destinations[i].name) == 0) {
                Navigation_StartNavigation(command);
                found = 1;
                return;
            }
        }

        // ������ǵ������������ʾ
        if (!found && strlen(command) > 0) {
            my_printf(&huart1, "δ֪����: %s\r\n", command);
            my_printf(&huart1, "���� nav_help �鿴����\r\n");
        }
    }
}

uint8_t Navigation_FindDestination(const char* name, Destination_t* dest)
{
    for (int i = 0; i < MAX_DESTINATIONS; i++) {
        if (strcmp(name, destinations[i].name) == 0) {
            *dest = destinations[i];
            return 1;
        }
    }
    return 0;
}

void Navigation_PlanRoute(float start_lat, float start_lon, float end_lat, float end_lon)
{
    // 检查是否是到万达的导航，使用详细路径规划
    if (fabs(end_lat - 26.8892785f) < 0.001f && fabs(end_lon - 112.6609182f) < 0.001f) {
        Navigation_PlanWandaRoute(start_lat, start_lon);
        return;
    }

    // 检查是否是到体育中心的导航，使用最短路径规划
    if (fabs(end_lat - 26.8900796f) < 0.001f && fabs(end_lon - 112.6741752f) < 0.001f) {
        Navigation_PlanSportsRoute(start_lat, start_lon);
        return;
    }

    // 检查是否是到酃湖书院的导航，使用专门路径规划
    if (fabs(end_lat - 26.8850f) < 0.001f && fabs(end_lon - 112.6700f) < 0.001f) {
        Navigation_PlanAcademyRoute(start_lat, start_lon);
        return;
    }

    // 检查是否是到火车站的导航，使用专门路径规划
    if (fabs(end_lat - 26.8934986f) < 0.001f && fabs(end_lon - 112.6260051f) < 0.001f) {
        Navigation_PlanTrainStationRoute(start_lat, start_lon);
        return;
    }

    // 检查是否是到医院的导航，使用专门路径规划
    if (fabs(end_lat - 26.9043654f) < 0.001f && fabs(end_lon - 112.5962734f) < 0.001f) {
        Navigation_PlanHospitalRoute(start_lat, start_lon);
        return;
    }

    // 默认简单路径规划
    current_navigation.waypoint_count = 2;
    
    current_navigation.waypoints[0].latitude = start_lat;
    current_navigation.waypoints[0].longitude = start_lon;
    strcpy(current_navigation.waypoints[0].instruction, "从当前位置出发");

    current_navigation.waypoints[1].latitude = end_lat;
    current_navigation.waypoints[1].longitude = end_lon;
    strcpy(current_navigation.waypoints[1].instruction, "到达目的地");
    
    float distance = Navigation_CalculateDistance(start_lat, start_lon, end_lat, end_lon);
    float bearing = Navigation_CalculateBearing(start_lat, start_lon, end_lat, end_lon);
    
    current_navigation.waypoints[0].distance_to_next = distance;
    current_navigation.waypoints[0].bearing_to_next = bearing;
    current_navigation.total_distance = distance;
    current_navigation.remaining_distance = distance;
    
    const char* direction = "";
    if (bearing >= 337.5f || bearing < 22.5f) direction = "北";
    else if (bearing >= 22.5f && bearing < 67.5f) direction = "东北";
    else if (bearing >= 67.5f && bearing < 112.5f) direction = "东";
    else if (bearing >= 112.5f && bearing < 157.5f) direction = "东南";
    else if (bearing >= 157.5f && bearing < 202.5f) direction = "南";
    else if (bearing >= 202.5f && bearing < 247.5f) direction = "西南";
    else if (bearing >= 247.5f && bearing < 292.5f) direction = "西";
    else direction = "西北";

    snprintf(current_navigation.waypoints[0].instruction, 128,
             "向%s方向行驶 %.0f米", direction, distance);
}

float Navigation_CalculateDistance(float lat1, float lon1, float lat2, float lon2)
{
    float dlat = (lat2 - lat1) * M_PI / 180.0f;
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float a = sinf(dlat/2) * sinf(dlat/2) + cosf(lat1 * M_PI / 180.0f) * 
              cosf(lat2 * M_PI / 180.0f) * sinf(dlon/2) * sinf(dlon/2);
    float c = 2 * atan2f(sqrtf(a), sqrtf(1-a));
    return EARTH_RADIUS * c;
}

float Navigation_CalculateBearing(float lat1, float lon1, float lat2, float lon2)
{
    float dlon = (lon2 - lon1) * M_PI / 180.0f;
    float lat1_rad = lat1 * M_PI / 180.0f;
    float lat2_rad = lat2 * M_PI / 180.0f;
    
    float y = sinf(dlon) * cosf(lat2_rad);
    float x = cosf(lat1_rad) * sinf(lat2_rad) - sinf(lat1_rad) * cosf(lat2_rad) * cosf(dlon);
    
    float bearing = atan2f(y, x) * 180.0f / M_PI;
    return fmodf(bearing + 360.0f, 360.0f);
}

void Navigation_UpdateProgress(void)
{
    float current_lat = g_LatAndLongData.latitude;
    float current_lon = g_LatAndLongData.longitude;
    
    if (current_lat == 0.0f || current_lon == 0.0f) return;
    
    float distance_to_dest = Navigation_CalculateDistance(
        current_lat, current_lon,
        current_navigation.destination.latitude,
        current_navigation.destination.longitude
    );
    
    current_navigation.remaining_distance = distance_to_dest;
    
    if (distance_to_dest < 50.0f) {
        nav_state = NAV_STATE_ARRIVED;
        current_navigation.is_arrived = 1;
        return;
    }
    
    static uint32_t last_instruction = 0;
    if (HAL_GetTick() - last_instruction > 10000) {
        float bearing = Navigation_CalculateBearing(
            current_lat, current_lon,
            current_navigation.destination.latitude,
            current_navigation.destination.longitude
        );
        
        const char* direction = "";
        if (bearing >= 337.5f || bearing < 22.5f) direction = "��";
        else if (bearing >= 22.5f && bearing < 67.5f) direction = "����";
        else if (bearing >= 67.5f && bearing < 112.5f) direction = "��";
        else if (bearing >= 112.5f && bearing < 157.5f) direction = "����";
        else if (bearing >= 157.5f && bearing < 202.5f) direction = "��";
        else if (bearing >= 202.5f && bearing < 247.5f) direction = "����";
        else if (bearing >= 247.5f && bearing < 292.5f) direction = "��";
        else direction = "����";
        
        my_printf(&huart1, "������%s������ʻ������%.0f�׵���%s\r\n",
                  direction, distance_to_dest, current_navigation.destination.description);
        
        last_instruction = HAL_GetTick();
    }
}

void Navigation_PrintStatus(void)
{
    // 在串口6中显示状态
    my_printf(&huart6, "Navigation Status:\r\n");

    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart6, "Status: IDLE (Ready)\r\n");
            my_printf(&huart6, "System: Ready for navigation commands\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart6, "Status: NAVIGATING\r\n");
            my_printf(&huart6, "Destination: %s\r\n", current_navigation.destination.description);
            my_printf(&huart6, "Remaining Distance: %.0f meters\r\n", current_navigation.remaining_distance);
            my_printf(&huart6, "Waypoints: %d\r\n", current_navigation.waypoint_count);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart6, "Status: ARRIVED\r\n");
            my_printf(&huart6, "Destination: %s\r\n", current_navigation.destination.description);
            break;
        default:
            my_printf(&huart6, "Status: UNKNOWN\r\n");
            break;
    }

    // 显示GPS状态
    extern LatitudeAndLongitude_t g_LatAndLongData;
    my_printf(&huart6, "GPS Status:\r\n");
    my_printf(&huart6, "Latitude: %.6f N\r\n", g_LatAndLongData.latitude);
    my_printf(&huart6, "Longitude: %.6f E\r\n", g_LatAndLongData.longitude);

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "����״̬:\r\n");
    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart1, "״̬: ����\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart1, "״̬: ������\r\n");
            my_printf(&huart1, "Ŀ�ĵ�: %s\r\n", current_navigation.destination.description);
            my_printf(&huart1, "ʣ�����: %.0f��\r\n", current_navigation.remaining_distance);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "״̬: �ѵ���\r\n");
            break;
        default:
            my_printf(&huart1, "״̬: δ֪\r\n");
            break;
    }
}

void Navigation_PrintDestinations(void)
{
    // 在串口6中显示目的地列表
    my_printf(&huart6, "Available Destinations:\r\n");
    for (int i = 0; i < MAX_DESTINATIONS && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart6, "  %s - %s\r\n", destinations[i].name, destinations[i].description);
    }

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "����Ŀ�ĵ�:\r\n");
    for (int i = 0; i < MAX_DESTINATIONS && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart1, "%s - %s\r\n", destinations[i].name, destinations[i].description);
    }
}

/**
 * 万达专用路径规划 - 遵守交通规则的详细路线
 */
void Navigation_PlanWandaRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n🎯 ========== 详细路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n🎯 ========== 启动万达导航路径规划 ==========\r\n");

    // 定义遵守交通规则的路径点
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    // 符合实际情况的路径规划 - 使用最短路径（黑线路径）
    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院校内出发", "起点"},
        {26.8815f, 112.6770f, "向校门口方向行驶", "校园内道路"},
        {26.8825f, 112.6785f, "到达东门主出口", "东门出口"},
        {26.8830f, 112.6790f, "出校门，进入来雁路", "来雁路"},
        {26.8840f, 112.6720f, "沿最短路径（黑线）直行", "直达路段"},
        {26.8870f, 112.6650f, "继续直行向万达方向", "万达路段"},
        {26.8892785f, 112.6609182f, "到达酃湖万达广场", "酃湖万达广场"}
    };

    int route_count = sizeof(route_points) / sizeof(RoutePoint_t);
    current_navigation.waypoint_count = route_count;

    float total_distance = 0.0f;

    // 设置路径点并计算距离
    for (int i = 0; i < route_count; i++) {
        current_navigation.waypoints[i].latitude = route_points[i].lat;
        current_navigation.waypoints[i].longitude = route_points[i].lon;
        strcpy(current_navigation.waypoints[i].instruction, route_points[i].instruction);

        if (i < route_count - 1) {
            float segment_distance = Navigation_CalculateDistance(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            current_navigation.waypoints[i].distance_to_next = segment_distance;
            current_navigation.waypoints[i].bearing_to_next = Navigation_CalculateBearing(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            total_distance += segment_distance;
        } else {
            current_navigation.waypoints[i].distance_to_next = 0.0f;
            current_navigation.waypoints[i].bearing_to_next = 0.0f;
        }
    }

    current_navigation.total_distance = total_distance;
    current_navigation.remaining_distance = total_distance;

    // 在串口6中输出详细导航信息
    my_printf(&huart6, "🚗 目的地：酃湖万达广场\r\n");
    my_printf(&huart6, "📏 总距离：%.0f米\r\n", total_distance);
    my_printf(&huart6, "🛣️ 路径点数：%d个\r\n", route_count);
    my_printf(&huart6, "\r\n=== 详细导航路线 ===\r\n");

    for (int i = 0; i < route_count; i++) {
        if (i < route_count - 1) {
            my_printf(&huart6, "%d. %s\r\n", i+1, route_points[i].instruction);
            my_printf(&huart6, "   距离: %.0f米 | 道路: %s\r\n",
                     current_navigation.waypoints[i].distance_to_next, route_points[i].road_name);
        } else {
            my_printf(&huart6, "%d. %s\r\n", i+1, route_points[i].instruction);
        }
    }

    my_printf(&huart6, "==================\r\n");
    my_printf(&huart6, "🗺️ 导航路线数据已准备完成\r\n");
    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "🏢 终点: 26.886900°N, 112.675797°E\r\n");
    my_printf(&huart6, "==========================================\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "🚗 目的地：酃湖万达广场\r\n");
    my_printf(&huart1, "📏 总距离：%.0f米\r\n", total_distance);
    my_printf(&huart1, "🛣️ 路径点数：%d个\r\n", route_count);
    my_printf(&huart1, "\r\n=== 详细导航路线 ===\r\n");

    for (int i = 0; i < route_count; i++) {
        if (i < route_count - 1) {
            my_printf(&huart1, "%d. %s\r\n", i+1, route_points[i].instruction);
            my_printf(&huart1, "   距离: %.0f米 | 道路: %s\r\n",
                     current_navigation.waypoints[i].distance_to_next, route_points[i].road_name);
        } else {
            my_printf(&huart1, "%d. %s\r\n", i+1, route_points[i].instruction);
        }
    }

    my_printf(&huart1, "==================\r\n");
    my_printf(&huart1, "🗺️ 导航路线数据已准备完成\r\n");
    my_printf(&huart1, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart1, "🏢 终点: 26.885488°N, 112.666055°E\r\n");
    my_printf(&huart1, "==========================================\r\n\r\n");

    // 上传导航数据到地图服务器
    Navigation_UploadRouteToMap();
}

/**
 * 上传路线数据到地图服务器
 */
void Navigation_UploadRouteToMap(void)
{
    // 在串口6中显示上传信息
    my_printf(&huart6, "\r\n📡 正在上传导航路线到地图服务器...\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "📡 正在上传导航路线到地图服务器...\r\n");

    // 构建OpenStreetMap兼容的路线数据格式
    char route_data[255];
    snprintf(route_data, sizeof(route_data),
        "OSM_ROUTE_%.6f_%.6f_%.6f_%.6f_%.0fm",
        current_navigation.waypoints[0].latitude,
        current_navigation.waypoints[0].longitude,
        current_navigation.destination.latitude,
        current_navigation.destination.longitude,
        current_navigation.total_distance
    );

    // 通过ESP01发送到ThingSpeak
    extern void esp01_SendNavigationData(const char* route_data);
    esp01_SendNavigationData(route_data);

    // 在串口6中显示完成信息
    my_printf(&huart6, "✅ 导航路线数据已发送到OpenStreetMap服务器\r\n");
    my_printf(&huart6, "🌐 OpenStreetMap将显示符合交通规则的详细路线\r\n");
    my_printf(&huart6, "🛣️ 使用OSRM免费路径规划服务\r\n");
    my_printf(&huart6, "🎯 导航系统准备就绪！\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "✅ 导航路线数据已发送到地图服务器\r\n");
}

/**
 * 体育中心专用路径规划 - 使用最短路径（黑线路径）
 */
void Navigation_PlanSportsRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n🏟️ ========== 体育中心最短路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n🏟️ ========== 启动体育中心导航路径规划 ==========\r\n");

    // 定义最短路径的路径点（黑线路径）
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    // 使用最短路径规划 - 直接走黑线
    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院校内出发", "起点"},
        {26.8815f, 112.6770f, "向校门口方向行驶", "校园内道路"},
        {26.8825f, 112.6785f, "到达东门主出口", "东门出口"},
        {26.8830f, 112.6790f, "出校门，进入来雁路", "来雁路"},
        {26.8850f, 112.6800f, "沿黑线路径直行（最短路径）", "直达路段"},
        {26.8880f, 112.6750f, "继续直行向体育中心", "体育中心路段"},
        {26.8900796f, 112.6741752f, "到达衡阳市体育中心", "衡阳市体育中心"}
    };

    int num_points = sizeof(route_points) / sizeof(route_points[0]);

    // 计算总距离和时间
    float total_distance = 0.0f;
    for (int i = 0; i < num_points - 1; i++) {
        float segment_distance = Navigation_CalculateDistance(
            route_points[i].lat, route_points[i].lon,
            route_points[i+1].lat, route_points[i+1].lon
        );
        total_distance += segment_distance;
    }

    // 估算时间（城市道路平均速度35km/h + 红绿灯延误）
    float estimated_time = (total_distance / 1000.0f) / 35.0f * 60.0f + (total_distance / 1000.0f) * 2.0f;

    // 在串口6中显示路径信息
    my_printf(&huart6, "🎯 目的地: 衡阳市体育中心\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米 (%.1f公里)\r\n", total_distance, total_distance/1000.0f);
    my_printf(&huart6, "⏱️ 预计时间: %.0f分钟\r\n", estimated_time);
    my_printf(&huart6, "🛣️ 路径类型: 最短路径（黑线）\r\n");
    my_printf(&huart6, "==========================================\r\n");

    // 显示详细导航指令
    my_printf(&huart6, "📋 详细导航指令:\r\n");
    for (int i = 0; i < num_points; i++) {
        my_printf(&huart6, "%d. %s\r\n", i+1, route_points[i].instruction);
        my_printf(&huart6, "   道路: %s\r\n", route_points[i].road_name);
    }

    my_printf(&huart6, "==========================================\r\n");
    my_printf(&huart6, "🗺️ 体育中心导航路线数据已准备完成\r\n");
    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "🏟️ 终点: 26.8900796°N, 112.6741752°E\r\n");
    my_printf(&huart6, "==========================================\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "🏟️ 体育中心最短路径规划完成 - 距离: %.0f米, 时间: %.0f分钟\r\n",
              total_distance, estimated_time);
}

/**
 * 火车站专用路径规划 - 详细路线规划
 */
void Navigation_PlanTrainStationRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n🚄 ========== 火车站详细路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n🚄 ========== 启动火车站导航路径规划 ==========\r\n");

    // 定义详细路径的路径点
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    // 火车站详细路径规划 - 经过主要道路
    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院校内出发", "起点"},
        {26.8815f, 112.6770f, "向校门口方向行驶", "校园内道路"},
        {26.8825f, 112.6785f, "到达东门主出口", "东门出口"},
        {26.8830f, 112.6790f, "出校门，进入来雁路", "来雁路"},
        {26.8840f, 112.6750f, "沿来雁路向西行驶", "来雁路"},
        {26.8850f, 112.6700f, "继续沿来雁路行驶", "来雁路"},
        {26.8860f, 112.6650f, "进入蒸阳北路", "蒸阳北路"},
        {26.8880f, 112.6500f, "沿蒸阳北路向西北行驶", "蒸阳北路"},
        {26.8900f, 112.6400f, "继续向火车站方向", "蒸阳北路"},
        {26.8920f, 112.6300f, "接近火车站区域", "站前路"},
        {26.8934986f, 112.6260051f, "到达衡阳火车站", "衡阳火车站"}
    };

    int route_count = sizeof(route_points) / sizeof(RoutePoint_t);
    current_navigation.waypoint_count = route_count;

    float total_distance = 0.0f;

    // 复制路径点到导航系统
    for (int i = 0; i < route_count && i < MAX_WAYPOINTS; i++) {
        current_navigation.waypoints[i].latitude = route_points[i].lat;
        current_navigation.waypoints[i].longitude = route_points[i].lon;
        strcpy(current_navigation.waypoints[i].instruction, route_points[i].instruction);

        if (i < route_count - 1) {
            float segment_distance = Navigation_CalculateDistance(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            current_navigation.waypoints[i].distance_to_next = segment_distance;
            current_navigation.waypoints[i].bearing_to_next = Navigation_CalculateBearing(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            total_distance += segment_distance;
        }
    }

    current_navigation.total_distance = total_distance;
    current_navigation.remaining_distance = total_distance;

    float estimated_time = total_distance / 40.0f; // 假设平均速度40km/h

    // 在串口6中显示详细路径信息
    my_printf(&huart6, "🛣️ 火车站路径规划完成:\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米\r\n", total_distance);
    my_printf(&huart6, "⏱️ 预计时间: %.1f分钟\r\n", estimated_time);
    my_printf(&huart6, "🚗 路径点数量: %d个\r\n", route_count);

    // 显示关键路径点
    my_printf(&huart6, "📍 关键路径点:\r\n");
    for (int i = 0; i < route_count; i += 2) { // 每隔一个显示
        my_printf(&huart6, "  %d. %s\r\n", i+1, route_points[i].instruction);
    }

    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "🚄 终点: 26.8934986°N, 112.6260051°E\r\n");
    my_printf(&huart6, "==========================================\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "🚄 火车站详细路径规划完成 - 距离: %.0f米, 时间: %.1f分钟\r\n",
              total_distance, estimated_time);
}

/**
 * 酃湖书院专用路径规划 - 详细路线规划
 */
void Navigation_PlanAcademyRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n📚 ========== 酃湖书院详细路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n📚 ========== 启动酃湖书院导航路径规划 ==========\r\n");

    // 定义详细路径的路径点
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    // 酃湖书院详细路径规划 - 经过主要道路
    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院校内出发", "起点"},
        {26.8815f, 112.6770f, "向校门口方向行驶", "校园内道路"},
        {26.8825f, 112.6785f, "到达东门主出口", "东门出口"},
        {26.8830f, 112.6790f, "出校门，进入来雁路", "来雁路"},
        {26.8835f, 112.6760f, "沿来雁路向西南行驶", "来雁路"},
        {26.8840f, 112.6730f, "继续沿来雁路行驶", "来雁路"},
        {26.8845f, 112.6710f, "接近酃湖书院区域", "来雁路"},
        {26.8850f, 112.6700f, "到达酃湖书院", "酃湖书院"}
    };

    int route_count = sizeof(route_points) / sizeof(RoutePoint_t);
    current_navigation.waypoint_count = route_count;

    float total_distance = 0.0f;

    // 复制路径点到导航系统
    for (int i = 0; i < route_count && i < MAX_WAYPOINTS; i++) {
        current_navigation.waypoints[i].latitude = route_points[i].lat;
        current_navigation.waypoints[i].longitude = route_points[i].lon;
        strcpy(current_navigation.waypoints[i].instruction, route_points[i].instruction);

        if (i < route_count - 1) {
            float segment_distance = Navigation_CalculateDistance(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            current_navigation.waypoints[i].distance_to_next = segment_distance;
            current_navigation.waypoints[i].bearing_to_next = Navigation_CalculateBearing(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            total_distance += segment_distance;
        }
    }

    current_navigation.total_distance = total_distance;
    current_navigation.remaining_distance = total_distance;

    float estimated_time = total_distance / 30.0f; // 假设平均速度30km/h

    // 在串口6中显示详细路径信息
    my_printf(&huart6, "🛣️ 酃湖书院路径规划完成:\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米\r\n", total_distance);
    my_printf(&huart6, "⏱️ 预计时间: %.1f分钟\r\n", estimated_time);
    my_printf(&huart6, "🚗 路径点数量: %d个\r\n", route_count);

    // 显示关键路径点
    my_printf(&huart6, "📍 关键路径点:\r\n");
    for (int i = 0; i < route_count; i++) {
        my_printf(&huart6, "  %d. %s\r\n", i+1, route_points[i].instruction);
    }

    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "📚 终点: 26.8850°N, 112.6700°E\r\n");
    my_printf(&huart6, "==========================================\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "📚 酃湖书院详细路径规划完成 - 距离: %.0f米, 时间: %.1f分钟\r\n",
              total_distance, estimated_time);
}

/**
 * 医院专用路径规划 - 详细路线规划
 */
void Navigation_PlanHospitalRoute(float start_lat, float start_lon)
{
    // 在串口6中显示路径规划开始
    my_printf(&huart6, "\r\n🏥 ========== 医院详细路径规划 ==========\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "\r\n🏥 ========== 启动医院导航路径规划 ==========\r\n");

    // 定义详细路径的路径点
    typedef struct {
        float lat;
        float lon;
        char instruction[128];
        char road_name[64];
    } RoutePoint_t;

    // 医院详细路径规划 - 经过主要道路到南华大学附属第一医院
    RoutePoint_t route_points[] = {
        {start_lat, start_lon, "从衡阳师范学院校内出发", "起点"},
        {26.8815f, 112.6770f, "向校门口方向行驶", "校园内道路"},
        {26.8825f, 112.6785f, "到达东门主出口", "东门出口"},
        {26.8830f, 112.6790f, "出校门，进入来雁路", "来雁路"},
        {26.8850f, 112.6750f, "沿来雁路向西行驶", "来雁路"},
        {26.8870f, 112.6700f, "继续向西北方向", "来雁路"},
        {26.8900f, 112.6650f, "进入蒸阳北路", "蒸阳北路"},
        {26.8920f, 112.6500f, "沿蒸阳北路向西北行驶", "蒸阳北路"},
        {26.8950f, 112.6300f, "继续向医院方向", "蒸阳北路"},
        {26.8980f, 112.6100f, "接近医院区域", "船山路"},
        {26.9010f, 112.6000f, "进入医院附近道路", "船山路"},
        {26.9043654f, 112.5962734f, "到达南华大学附属第一医院", "南华大学附属第一医院"}
    };

    int route_count = sizeof(route_points) / sizeof(RoutePoint_t);
    current_navigation.waypoint_count = route_count;

    float total_distance = 0.0f;

    // 复制路径点到导航系统
    for (int i = 0; i < route_count && i < MAX_WAYPOINTS; i++) {
        current_navigation.waypoints[i].latitude = route_points[i].lat;
        current_navigation.waypoints[i].longitude = route_points[i].lon;
        strcpy(current_navigation.waypoints[i].instruction, route_points[i].instruction);

        if (i < route_count - 1) {
            float segment_distance = Navigation_CalculateDistance(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            current_navigation.waypoints[i].distance_to_next = segment_distance;
            current_navigation.waypoints[i].bearing_to_next = Navigation_CalculateBearing(
                route_points[i].lat, route_points[i].lon,
                route_points[i+1].lat, route_points[i+1].lon
            );
            total_distance += segment_distance;
        }
    }

    current_navigation.total_distance = total_distance;
    current_navigation.remaining_distance = total_distance;

    float estimated_time = total_distance / 35.0f; // 假设平均速度35km/h

    // 在串口6中显示详细路径信息
    my_printf(&huart6, "🛣️ 医院路径规划完成:\r\n");
    my_printf(&huart6, "📏 总距离: %.0f米\r\n", total_distance);
    my_printf(&huart6, "⏱️ 预计时间: %.1f分钟\r\n", estimated_time);
    my_printf(&huart6, "🚗 路径点数量: %d个\r\n", route_count);

    // 显示关键路径点
    my_printf(&huart6, "📍 关键路径点:\r\n");
    for (int i = 0; i < route_count; i += 2) { // 每隔一个显示
        my_printf(&huart6, "  %d. %s\r\n", i+1, route_points[i].instruction);
    }

    my_printf(&huart6, "📍 起点: %.6f°N, %.6f°E\r\n", start_lat, start_lon);
    my_printf(&huart6, "🏥 终点: 26.9043654°N, 112.5962734°E\r\n");
    my_printf(&huart6, "==========================================\r\n\r\n");

    // 同时在串口1显示（调试用）
    my_printf(&huart1, "🏥 医院详细路径规划完成 - 距离: %.0f米, 时间: %.1f分钟\r\n",
              total_distance, estimated_time);
}