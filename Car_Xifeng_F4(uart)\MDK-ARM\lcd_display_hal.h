#ifndef __LCD_DISPLAY_HAL_H
#define __LCD_DISPLAY_HAL_H		

#include "MyDefine.h"
#include "lcd_init_hal.h"

// 基本绘图函数
void LCD_Fill(uint16_t xsta, uint16_t ysta, uint16_t xend, uint16_t yend, uint16_t color);
void LCD_DrawPoint(uint16_t x, uint16_t y, uint16_t color);
void LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
void LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t color);
void Draw_Circle(uint16_t x0, uint16_t y0, uint8_t r, uint16_t color);

// 字符点阵生成函数
uint8_t LCD_GetDigitPixel(uint8_t digit, uint8_t x, uint8_t y, uint8_t w, uint8_t h);
uint8_t LCD_GetLetterPixel(uint8_t letter, uint8_t x, uint8_t y, uint8_t w, uint8_t h);

// 字符和字符串显示函数
void LCD_ShowChar(uint16_t x, uint16_t y, uint8_t num, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowString(uint16_t x, uint16_t y, const uint8_t *p, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowIntNum(uint16_t x, uint16_t y, uint16_t num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey);
void LCD_ShowFloatNum1(uint16_t x, uint16_t y, float num, uint8_t len, uint16_t fc, uint16_t bc, uint8_t sizey);

// 中文显示函数
void LCD_ShowChinese(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowChinese12x12(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowChinese16x16(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowChinese24x24(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);
void LCD_ShowChinese32x32(uint16_t x, uint16_t y, uint8_t *s, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);

// 中英文混合显示函数
void LCD_ShowMixedString(uint16_t x, uint16_t y, const char *str, uint16_t fc, uint16_t bc, uint8_t sizey, uint8_t mode);

// 图片显示函数
void LCD_ShowPicture(uint16_t x, uint16_t y, uint16_t length, uint16_t width, const uint8_t pic[]);

// 工具函数
uint32_t mypow(uint8_t m, uint8_t n);

// 颜色定义
#define WHITE         	 0xFFFF
#define BLACK         	 0x0000	  
#define BLUE           	 0x001F  
#define BRED             0XF81F
#define GRED 			 0XFFE0
#define GBLUE			 0X07FF
#define RED           	 0xF800
#define MAGENTA       	 0xF81F
#define GREEN         	 0x07E0
#define CYAN          	 0x7FFF
#define YELLOW        	 0xFFE0
#define BROWN 			 0XBC40 // 棕色
#define BRRED 			 0XFC07 // 棕红色
#define GRAY  			 0X8430 // 灰色
#define DARKBLUE      	 0X01CF	// 深蓝色
#define LIGHTBLUE      	 0X7D7C	// 浅蓝色  
#define GRAYBLUE       	 0X5458 // 灰蓝色
#define LIGHTGREEN     	 0X841F // 浅绿色
#define LGRAY 			 0XC618 // 浅灰色(PANNEL),窗体背景色
#define LGRAYBLUE        0XA651 // 浅灰蓝色(中间层颜色)
#define LBBLUE           0X2B12 // 浅棕蓝色(选择条目的反色)

#endif
