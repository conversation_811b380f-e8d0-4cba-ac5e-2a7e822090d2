#include "uart2_app.h"
#include "../MDK-ARM/esp01_app.h"

// 函数声明
extern void esp01_SetConnected(void);

extern uint8_t uart2_rx_dma_buffer[UART2_BUFFER_SIZE];
extern uint8_t uart2_ring_buffer_input[UART2_BUFFER_SIZE];
extern struct rt_ringbuffer uart2_ring_buffer;
extern uint8_t uart2_data_buffer[UART2_BUFFER_SIZE];

void Uart2_Init(void)
{
    rt_ringbuffer_init(&uart2_ring_buffer, uart2_ring_buffer_input, UART2_BUFFER_SIZE);
    HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, UART2_BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
}



void Uart2_Task(void)
{
    uint16_t uart2_data_len = rt_ringbuffer_data_len(&uart2_ring_buffer);
    static uint32_t last_debug_time = 0;
    static uint32_t last_receive_time = 0;
    if(uart2_data_len > 0) {
        last_receive_time = HAL_GetTick();
    }
    if(HAL_GetTick() - last_receive_time > 10000)
    {
        HAL_UART_DMAStop(&huart2);
        HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, UART2_BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
        last_receive_time = HAL_GetTick();
    }
    if(uart2_data_len > 0)
    {
        rt_ringbuffer_get(&uart2_ring_buffer, uart2_data_buffer, uart2_data_len);
        uart2_data_buffer[uart2_data_len] = '\0';

        // 增强的ESP-01响应处理
        my_printf(&huart1, "[RX] ESP-01 Response (%d bytes): %s", uart2_data_len, uart2_data_buffer);

        // 检查基本OK响应
        if (strstr((char*)uart2_data_buffer, "OK") != NULL && uart2_data_len < 10)
        {
            my_printf(&huart1, "✅ ESP-01: Command executed successfully\r\n");
        }
        // 检查连接状态响应
        else if (strstr((char*)uart2_data_buffer, "WIFI CONNECTED") != NULL)
        {
            my_printf(&huart1, "✅ ESP-01: WiFi connection established\r\n");
            esp01_SetConnected();
        }
        else if (strstr((char*)uart2_data_buffer, "WIFI GOT IP") != NULL)
        {
            my_printf(&huart1, "✅ ESP-01: IP address obtained\r\n");
            esp01_SetConnected();
        }
        else if (strstr((char*)uart2_data_buffer, "ready") != NULL)
        {
            my_printf(&huart1, "🔄 ESP-01: Module ready, starting initialization\r\n");
            esp01_StartInit();
        }
        // 检查TCP连接响应 - 优先级很高
        else if (strstr((char*)uart2_data_buffer, "CONNECT") != NULL)
        {
            // 检查是否是"ALREADY CONNECTED"错误
            if (strstr((char*)uart2_data_buffer, "ALREADY CONNECTED") != NULL)
            {
                my_printf(&huart1, "⚠️ ESP-01: Connection already exists\r\n");
                // 对于ALREADY CONNECTED，也认为连接成功
                esp01_SetTCPConnected();
            }
            else if (strstr((char*)uart2_data_buffer, "CONNECT") != NULL &&
                     strstr((char*)uart2_data_buffer, "DISCONNECT") == NULL)
            {
                my_printf(&huart1, "✅ ESP-01: TCP connection established successfully!\r\n");
                esp01_SetTCPConnected();
            }
        }
        // 检查连接状态响应
        else if (strstr((char*)uart2_data_buffer, "STATUS:") != NULL)
        {
            if (strstr((char*)uart2_data_buffer, "STATUS:3") != NULL)
            {
                my_printf(&huart1, "✅ ESP-01: Connection status - Connected\r\n");
                esp01_SetTCPConnected();
            }
            else if (strstr((char*)uart2_data_buffer, "STATUS:2") != NULL)
            {
                my_printf(&huart1, "📡 ESP-01: Connection status - Got IP\r\n");
            }
            else
            {
                my_printf(&huart1, "📊 ESP-01: Connection status info\r\n");
            }
        }
        // 检查数据发送提示符 - 最高优先级
        else if (strstr((char*)uart2_data_buffer, ">") != NULL)
        {
            my_printf(&huart1, "📝 ESP-01: Ready to send data (> prompt received)\r\n");
            esp01_SetDataSendReady();
        }
        // 检查数据发送成功
        else if (strstr((char*)uart2_data_buffer, "SEND OK") != NULL)
        {
            my_printf(&huart1, "📤 ESP-01: Data transmission successful\r\n");
        }
        // 检查连接关闭
        else if (strstr((char*)uart2_data_buffer, "CLOSED") != NULL)
        {
            my_printf(&huart1, "🔌 ESP-01: Connection closed\r\n");
            esp01_ResetTCPState();
        }
        else if (strstr((char*)uart2_data_buffer, "DISCONNECT") != NULL)
        {
            my_printf(&huart1, "🔌 ESP-01: Connection disconnected\r\n");
            esp01_ResetTCPState();
        }
        // 检查DNS解析响应
        else if (strstr((char*)uart2_data_buffer, "+CIPDOMAIN:") != NULL)
        {
            my_printf(&huart1, "🔍 ESP-01: DNS resolution result received\r\n");
        }
        // 检查错误响应
        else if (strstr((char*)uart2_data_buffer, "DNS Fail") != NULL)
        {
            my_printf(&huart1, "❌ ESP-01: DNS resolution failed - network issue\r\n");
            esp01_ResetTCPState();
        }
        else if (strstr((char*)uart2_data_buffer, "CONNECT FAIL") != NULL)
        {
            my_printf(&huart1, "❌ ESP-01: TCP connection failed - server unreachable\r\n");
            esp01_ResetTCPState();
        }
        else if (strstr((char*)uart2_data_buffer, "ERROR") != NULL)
        {
            // 检查具体错误类型
            if (strstr((char*)uart2_data_buffer, "AT+CIPCLOSE") != NULL)
            {
                my_printf(&huart1, "ℹ️ ESP-01: Connection already closed\r\n");
            }
            else if (strstr((char*)uart2_data_buffer, "AT+CIPSTART") != NULL)
            {
                my_printf(&huart1, "❌ ESP-01: TCP connection start failed\r\n");
                esp01_ResetTCPState();
            }
            else if (strstr((char*)uart2_data_buffer, "AT+CIPSEND") != NULL)
            {
                my_printf(&huart1, "❌ ESP-01: Data send command failed\r\n");
            }
            else
            {
                my_printf(&huart1, "❌ ESP-01: Command error\r\n");
            }
        }
        else if (strstr((char*)uart2_data_buffer, "FAIL") != NULL)
        {
            my_printf(&huart1, "❌ ESP-01: Operation failed\r\n");
            esp01_ResetTCPState();
        }

        // 检查HTTP响应 - 增强版本
        else if (strstr((char*)uart2_data_buffer, "HTTP/1.1") != NULL)
        {
            if (strstr((char*)uart2_data_buffer, "200 OK") != NULL)
            {
                my_printf(&huart1, "✅ HTTP: ThingSpeak responded with 200 OK\r\n");
                my_printf(&huart1, "🎉 数据上传成功！\r\n");
            }
            else if (strstr((char*)uart2_data_buffer, "400") != NULL)
            {
                my_printf(&huart1, "❌ HTTP 400: Bad Request - 检查API密钥或参数\r\n");
            }
            else if (strstr((char*)uart2_data_buffer, "401") != NULL)
            {
                my_printf(&huart1, "❌ HTTP 401: Unauthorized - API密钥错误\r\n");
            }
            else
            {
                my_printf(&huart1, "⚠️ HTTP: 非200响应 - %s\r\n", uart2_data_buffer);
            }
        }
        // 检查ThingSpeak特有响应
        else if (strstr((char*)uart2_data_buffer, "thingspeak") != NULL ||
                 strstr((char*)uart2_data_buffer, "entry_id") != NULL)
        {
            my_printf(&huart1, "📊 ThingSpeak: 收到服务器响应\r\n");
            my_printf(&huart1, "📝 响应内容: %s\r\n", uart2_data_buffer);
        }
        // 检查数字响应 (ThingSpeak成功时返回entry_id数字)
        else if (uart2_data_len < 10 && uart2_data_buffer[0] >= '0' && uart2_data_buffer[0] <= '9')
        {
            my_printf(&huart1, "🎯 ThingSpeak: Entry ID = %s\r\n", uart2_data_buffer);
            my_printf(&huart1, "✅ 数据上传成功！\r\n");
        }

        // 清空缓冲区
        memset(uart2_data_buffer, 0, uart2_data_len);
    }
}
