// ThingSpeak配置
const CONFIG = {
    // ThingSpeak设置 - 您的实际密钥
    THINGSPEAK: {
        CHANNEL_ID: '3014831',                // 您的Channel ID
        READ_API_KEY: 'V64RR7CZJ9Z4Q7ED',     // 您的Read API Key (修复)
        WRITE_API_KEY: 'LU22ZUP4ZTFK4IY9',    // 您的Write API Key
        BASE_URL: 'https://api.thingspeak.com',
        
        // 字段映射
        FIELDS: {
            LATITUDE: 'field1',   // 纬度字段
            LONGITUDE: 'field2',  // 经度字段
            ALTITUDE: 'field3'    // 海拔字段
        }
    },
    
    // 地图设置
    MAP: {
        // 衡阳师范学院坐标 (真正准确位置)
        DEFAULT_CENTER: [26.88693, 112.675813],
        DEFAULT_ZOOM: 16,
        MAX_ZOOM: 18,
        MIN_ZOOM: 10,
        
        // OpenStreetMap图层配置
        TILE_LAYERS: {
            OPENSTREETMAP: {
                url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                name: 'OpenStreetMap'
            },
            SATELLITE: {
                url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                attribution: '&copy; <a href="https://www.esri.com/">Esri</a>',
                name: '卫星图'
            }
        }
    },
    
    // 应用设置
    APP: {
        UPDATE_INTERVAL: 10000,    // 默认更新间隔(毫秒)
        MAX_TRACK_POINTS: 100,     // 最大轨迹点数
        AUTO_CENTER: true,         // 自动居中
        SHOW_TRACK: true,          // 显示轨迹
        
        // 设备信息
        DEVICE: {
            ID: 'CAR-001',
            NAME: '衡阳追踪设备',
            ICON: '🚗'
        }
    },
    
    // 调试模式
    DEBUG: true
};

// 日志函数
function log(message, type = 'info') {
    if (!CONFIG.DEBUG) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const prefix = `[${timestamp}] GPS Tracker:`;
    
    switch(type) {
        case 'error':
            console.error(prefix, message);
            break;
        case 'warn':
            console.warn(prefix, message);
            break;
        case 'success':
            console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
            break;
        default:
            console.log(prefix, message);
    }
}

// 工具函数
const Utils = {
    // 格式化时间
    formatTime(date) {
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    // 格式化坐标
    formatCoordinate(coord, decimals = 6) {
        return parseFloat(coord).toFixed(decimals);
    },
    
    // 计算两点间距离(米)
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371e3; // 地球半径(米)
        const φ1 = lat1 * Math.PI/180;
        const φ2 = lat2 * Math.PI/180;
        const Δφ = (lat2-lat1) * Math.PI/180;
        const Δλ = (lon2-lon1) * Math.PI/180;

        const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
                  Math.cos(φ1) * Math.cos(φ2) *
                  Math.sin(Δλ/2) * Math.sin(Δλ/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

        return R * c;
    },
    
    // 生成OpenStreetMap链接
    generateMapLink(lat, lon, zoom = 16) {
        return `https://www.openstreetmap.org/?mlat=${lat}&mlon=${lon}&zoom=${zoom}`;
    },
    
    // 显示通知
    showNotification(message, type = 'info') {
        // 简单的通知实现，可以后续扩展
        console.log(`通知 [${type}]: ${message}`);
        
        // 更新状态栏
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status ${type === 'error' ? 'offline' : 'online'}`;
        }
    }
};

// 导出配置和工具
window.CONFIG = CONFIG;
window.Utils = Utils;
window.log = log;
