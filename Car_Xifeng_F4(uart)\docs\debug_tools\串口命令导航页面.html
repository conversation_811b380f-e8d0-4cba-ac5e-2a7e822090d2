<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 串口命令导航系统</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status-panel {
            background: rgba(255,255,255,0.1);
            margin: 20px;
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .status-value {
            font-weight: bold;
            color: #00ff88;
        }
        #map {
            height: 500px;
            margin: 20px;
            border-radius: 15px;
            border: 3px solid rgba(255,255,255,0.3);
        }
        .btn {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        .log-container {
            background: rgba(0,0,0,0.5);
            margin: 20px;
            padding: 15px;
            border-radius: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📱 串口命令导航系统</h1>
        <p>通过串口调试助手发送命令 → ESP01 → ThingSpeak → 网页端路径规划</p>
    </div>

    <div class="status-panel">
        <h3>📊 系统状态</h3>
        <div class="status-item">
            <span>📡 ThingSpeak连接:</span>
            <span id="thingspeakStatus" class="status-value">检查中...</span>
        </div>
        <div class="status-item">
            <span>📍 最新GPS位置:</span>
            <span id="gpsPosition" class="status-value">等待数据...</span>
        </div>
        <div class="status-item">
            <span>🧭 导航命令:</span>
            <span id="navigationCommand" class="status-value">无</span>
        </div>
        <div class="status-item">
            <span>⏰ 最后更新:</span>
            <span id="lastUpdate" class="status-value">--</span>
        </div>
        <div class="status-item">
            <span>🎯 WANDA命令状态:</span>
            <span id="wandaCommandStatus" class="status-value">等待中...</span>
        </div>
    </div>

    <div class="controls">
        <button class="btn" onclick="startMonitoring()">🚀 开始监听</button>
        <button class="btn" onclick="stopMonitoring()">⏹️ 停止监听</button>
        <button class="btn" onclick="testConnection()">🔧 测试连接</button>
        <button class="btn" onclick="clearLogs()">🧹 清除日志</button>
    </div>

    <div id="map"></div>

    <div class="log-container" id="logContainer">
        <div style="color: #00ff00;">[系统] 串口命令导航系统已启动</div>
        <div style="color: #ffff00;">[提示] 请在串口调试助手中发送 "wanda" 命令</div>
        <div style="color: #00bfff;">[说明] 系统将自动从ThingSpeak读取导航数据</div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // ThingSpeak配置
        const CONFIG = {
            CHANNEL_ID: '3014831',
            READ_API_KEY: 'V64RR7CZJ9Z4O7ED',
            BASE_URL: 'https://api.thingspeak.com'
        };

        // 全局变量
        let map;
        let currentMarker;
        let destinationMarker;
        let routeLine;
        let monitoringInterval;
        let isMonitoring = false;

        // 初始化地图
        function initMap() {
            // 衡阳师范学院坐标
            const defaultLat = 26.8812;
            const defaultLon = 112.6769;

            map = L.map('map').setView([defaultLat, defaultLon], 13);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // 添加默认标记
            currentMarker = L.marker([defaultLat, defaultLon])
                .addTo(map)
                .bindPopup('📍 当前位置')
                .openPopup();

            log('🗺️ 地图初始化完成', 'success');
        }

        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString();
            const colors = {
                info: '#00bfff',
                success: '#00ff00',
                error: '#ff4444',
                warning: '#ffaa00'
            };

            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${time}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态显示
        function updateStatus(elementId, value, pulse = false) {
            const element = document.getElementById(elementId);
            element.textContent = value;
            if (pulse) {
                element.classList.add('pulse');
                setTimeout(() => element.classList.remove('pulse'), 2000);
            }
        }

        // 从ThingSpeak获取最新数据
        async function fetchLatestData() {
            try {
                const url = `${CONFIG.BASE_URL}/channels/${CONFIG.CHANNEL_ID}/feeds/last.json?api_key=${CONFIG.READ_API_KEY}`;
                const response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                if (!data || !data.created_at) {
                    throw new Error('无数据');
                }

                // 解析GPS数据
                const lat = parseFloat(data.field1);
                const lon = parseFloat(data.field2);
                const alt = parseFloat(data.field3);
                const navData = data.field4;

                // 更新状态显示
                updateStatus('thingspeakStatus', '✅ 连接正常');
                updateStatus('gpsPosition', `${lat.toFixed(6)}°N, ${lon.toFixed(6)}°E`);
                updateStatus('lastUpdate', new Date(data.created_at).toLocaleString());

                // 检查导航命令 - 修复格式检测
                if (navData && navData.includes('WANDA_')) {
                    updateStatus('navigationCommand', '🛍️ 万达广场导航', true);
                    updateStatus('wandaCommandStatus', '✅ 已接收到WANDA命令', true);
                    log(`🧭 检测到万达导航命令: ${navData}`, 'success');
                    log(`🎉 WANDA命令上传成功！`, 'success');
                    processNavigationCommand(navData, lat, lon);
                } else if (navData && navData.trim() !== '') {
                    updateStatus('navigationCommand', `📍 ${navData}`);
                    log(`📍 检测到其他数据: ${navData}`, 'info');
                } else {
                    updateStatus('navigationCommand', '无');
                    updateStatus('wandaCommandStatus', '等待中...');
                }

                // 更新地图位置
                if (lat && lon && !isNaN(lat) && !isNaN(lon)) {
                    updateMapPosition(lat, lon);
                }

                return data;

            } catch (error) {
                updateStatus('thingspeakStatus', '❌ 连接失败');
                log(`❌ 获取数据失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 处理导航命令
        function processNavigationCommand(navData, currentLat, currentLon) {
            try {
                // 解析导航数据: WANDA_经度1_纬度1_经度2_纬度2 (修复格式)
                const parts = navData.split('_');
                log(`🔍 解析导航数据: ${navData}`, 'info');
                log(`📊 分割结果: ${parts.length} 部分 - [${parts.join(', ')}]`, 'info');

                if (parts.length >= 5 && parts[0] === 'WANDA') {
                    const startLon = parseFloat(parts[1]);
                    const startLat = parseFloat(parts[2]);
                    const endLon = parseFloat(parts[3]);
                    const endLat = parseFloat(parts[4]);

                    // 验证坐标有效性
                    if (!isNaN(startLat) && !isNaN(startLon) && !isNaN(endLat) && !isNaN(endLon)) {
                        log(`🎯 规划路径: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`, 'success');

                        // 在地图上显示路径
                        showRoute(startLat, startLon, endLat, endLon);

                        // 显示成功消息
                        log(`✅ 万达导航路径已显示在地图上！`, 'success');
                    } else {
                        log(`❌ 坐标数据无效: lat1=${startLat}, lon1=${startLon}, lat2=${endLat}, lon2=${endLon}`, 'error');
                    }
                } else {
                    log(`⚠️ 导航数据格式不正确，期待: WANDA_经度1_纬度1_经度2_纬度2`, 'warning');
                }
            } catch (error) {
                log(`❌ 解析导航命令失败: ${error.message}`, 'error');
            }
        }

        // 在地图上显示路径
        function showRoute(startLat, startLon, endLat, endLon) {
            // 清除之前的路径
            if (routeLine) {
                map.removeLayer(routeLine);
            }
            if (destinationMarker) {
                map.removeLayer(destinationMarker);
            }

            // 更新起点标记
            if (currentMarker) {
                currentMarker.setLatLng([startLat, startLon]);
            }

            // 添加终点标记
            destinationMarker = L.marker([endLat, endLon])
                .addTo(map)
                .bindPopup('🛍️ 酃湖万达广场');

            // 添加路径线
            routeLine = L.polyline([
                [startLat, startLon],
                [endLat, endLon]
            ], {
                color: 'red',
                weight: 4,
                opacity: 0.8
            }).addTo(map);

            // 调整地图视图
            const bounds = L.latLngBounds([
                [startLat, startLon],
                [endLat, endLon]
            ]);
            map.fitBounds(bounds, { padding: [20, 20] });

            log('🗺️ 路径已显示在地图上', 'success');
        }

        // 更新地图位置
        function updateMapPosition(lat, lon) {
            if (currentMarker) {
                currentMarker.setLatLng([lat, lon]);
            }
        }

        // 开始监听
        function startMonitoring() {
            if (isMonitoring) return;

            isMonitoring = true;
            log('🚀 开始监听ThingSpeak数据...', 'info');

            // 立即获取一次数据
            fetchLatestData();

            // 每10秒获取一次数据
            monitoringInterval = setInterval(fetchLatestData, 10000);
        }

        // 停止监听
        function stopMonitoring() {
            if (!isMonitoring) return;

            isMonitoring = false;
            clearInterval(monitoringInterval);
            log('⏹️ 已停止监听', 'warning');
        }

        // 测试连接
        async function testConnection() {
            log('🔧 测试ThingSpeak连接...', 'info');
            const data = await fetchLatestData();
            if (data) {
                log('✅ 连接测试成功', 'success');
            } else {
                log('❌ 连接测试失败', 'error');
            }
        }

        // 清除日志
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('🧹 日志已清除', 'info');
        }

        // 页面加载完成
        window.onload = function() {
            initMap();
            log('📱 串口命令导航系统已就绪', 'success');
            log('💡 使用说明:', 'info');
            log('  1. 在串口调试助手中发送 "wanda" 命令', 'info');
            log('  2. 单片机通过ESP01上传到ThingSpeak', 'info');
            log('  3. 本页面自动读取并显示路径', 'info');
            log('  4. 观察 "WANDA命令状态" 是否显示 "✅ 已接收到WANDA命令"', 'info');
            log('🔍 检测格式: WANDA_经度1_纬度1_经度2_纬度2', 'info');

            // 自动开始监听
            setTimeout(startMonitoring, 2000);
        };
    </script>
</body>
</html>
