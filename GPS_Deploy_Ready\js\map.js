// 地图管理类
class GPSMap {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.currentMarker = null;
        this.trackPolyline = null;
        this.trackPoints = [];
        this.isAutoCenter = CONFIG.APP.AUTO_CENTER;
        this.showTrack = CONFIG.APP.SHOW_TRACK;

        // 导航相关
        this.navigationRoute = null;
        this.navigationMarkers = [];
        this.isNavigating = false;

        this.initMap();
        this.setupEventListeners();
    }

    // 初始化地图
    initMap() {
        try {
            // 创建地图实例
            this.map = L.map(this.containerId, {
                center: CONFIG.MAP.DEFAULT_CENTER,
                zoom: CONFIG.MAP.DEFAULT_ZOOM,
                maxZoom: CONFIG.MAP.MAX_ZOOM,
                minZoom: CONFIG.MAP.MIN_ZOOM,
                zoomControl: true
            });

            // 添加OpenStreetMap图层
            const osmLayer = L.tileLayer(
                CONFIG.MAP.TILE_LAYERS.OPENSTREETMAP.url,
                {
                    attribution: CONFIG.MAP.TILE_LAYERS.OPENSTREETMAP.attribution,
                    maxZoom: CONFIG.MAP.MAX_ZOOM
                }
            );

            // 添加卫星图层
            const satelliteLayer = L.tileLayer(
                CONFIG.MAP.TILE_LAYERS.SATELLITE.url,
                {
                    attribution: CONFIG.MAP.TILE_LAYERS.SATELLITE.attribution,
                    maxZoom: CONFIG.MAP.MAX_ZOOM
                }
            );

            // 默认使用OpenStreetMap
            osmLayer.addTo(this.map);

            // 添加图层控制
            const baseLayers = {
                "OpenStreetMap": osmLayer,
                "卫星图": satelliteLayer
            };
            L.control.layers(baseLayers).addTo(this.map);

            // 添加比例尺
            L.control.scale({
                metric: true,
                imperial: false,
                position: 'bottomleft'
            }).addTo(this.map);

            log('地图初始化成功', 'success');

        } catch (error) {
            log(`地图初始化失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 地图点击事件
        this.map.on('click', (e) => {
            const { lat, lng } = e.latlng;
            log(`地图点击: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        });

        // 地图缩放事件
        this.map.on('zoomend', () => {
            log(`地图缩放级别: ${this.map.getZoom()}`);
        });
    }

    // 更新当前位置
    updateCurrentLocation(gpsData) {
        if (!gpsData.latitude || !gpsData.longitude) {
            log('GPS数据无效', 'warn');
            return;
        }

        const { latitude, longitude, altitude, timestamp } = gpsData;
        const position = [latitude, longitude];

        // 更新或创建当前位置标记
        if (this.currentMarker) {
            this.currentMarker.setLatLng(position);
        } else {
            // 创建自定义图标
            const customIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div style="background: #e74c3c; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3);"></div>`,
                iconSize: [26, 26],
                iconAnchor: [13, 13]
            });

            this.currentMarker = L.marker(position, { icon: customIcon })
                .addTo(this.map);
        }

        // 更新标记弹窗
        const popupContent = `
            <div style="text-align: center;">
                <h4>🚗 ${CONFIG.APP.DEVICE.NAME}</h4>
                <p><strong>位置:</strong> ${Utils.formatCoordinate(latitude)}, ${Utils.formatCoordinate(longitude)}</p>
                ${altitude ? `<p><strong>海拔:</strong> ${altitude.toFixed(1)}m</p>` : ''}
                <p><strong>时间:</strong> ${Utils.formatTime(timestamp)}</p>
                <p><a href="${Utils.generateMapLink(latitude, longitude)}" target="_blank">在OpenStreetMap中查看</a></p>
            </div>
        `;
        this.currentMarker.bindPopup(popupContent);

        // 添加到轨迹
        if (this.showTrack) {
            this.addTrackPoint(position, timestamp);
        }

        // 自动居中
        if (this.isAutoCenter) {
            this.centerOnLocation(position);
        }

        log(`位置更新: ${Utils.formatCoordinate(latitude)}, ${Utils.formatCoordinate(longitude)}`, 'success');
    }

    // 添加轨迹点
    addTrackPoint(position, timestamp) {
        // 添加新点
        this.trackPoints.push({
            position: position,
            timestamp: timestamp
        });

        // 限制轨迹点数量
        if (this.trackPoints.length > CONFIG.APP.MAX_TRACK_POINTS) {
            this.trackPoints.shift();
        }

        // 更新轨迹线
        this.updateTrackPolyline();
    }

    // 更新轨迹线
    updateTrackPolyline() {
        if (this.trackPoints.length < 2) return;

        const positions = this.trackPoints.map(point => point.position);

        if (this.trackPolyline) {
            this.trackPolyline.setLatLngs(positions);
        } else {
            this.trackPolyline = L.polyline(positions, {
                color: '#3498db',
                weight: 4,
                opacity: 0.8,
                smoothFactor: 1
            }).addTo(this.map);

            // 添加轨迹信息
            this.trackPolyline.bindPopup(`
                <div>
                    <h4>📍 移动轨迹</h4>
                    <p>轨迹点数: ${this.trackPoints.length}</p>
                    <p>开始时间: ${Utils.formatTime(this.trackPoints[0].timestamp)}</p>
                    <p>最新时间: ${Utils.formatTime(this.trackPoints[this.trackPoints.length - 1].timestamp)}</p>
                </div>
            `);
        }
    }

    // 居中到指定位置
    centerOnLocation(position, zoom = null) {
        const targetZoom = zoom || this.map.getZoom();
        this.map.setView(position, targetZoom);
    }

    // 清除轨迹
    clearTrack() {
        if (this.trackPolyline) {
            this.map.removeLayer(this.trackPolyline);
            this.trackPolyline = null;
        }
        this.trackPoints = [];
        log('轨迹已清除', 'success');
    }

    // 设置自动居中
    setAutoCenter(enabled) {
        this.isAutoCenter = enabled;
        log(`自动居中: ${enabled ? '开启' : '关闭'}`);
    }

    // 设置轨迹显示
    setShowTrack(enabled) {
        this.showTrack = enabled;
        if (!enabled) {
            this.clearTrack();
        }
        log(`轨迹显示: ${enabled ? '开启' : '关闭'}`);
    }

    // 加载历史轨迹
    loadHistoryTrack(historyData) {
        if (!historyData || historyData.length === 0) {
            log('没有历史数据可加载', 'warn');
            return;
        }

        // 清除现有轨迹
        this.clearTrack();

        // 添加历史轨迹点
        historyData.forEach(data => {
            if (data.latitude && data.longitude) {
                this.addTrackPoint([data.latitude, data.longitude], data.timestamp);
            }
        });

        // 如果有轨迹点，居中显示
        if (this.trackPoints.length > 0) {
            const bounds = L.latLngBounds(this.trackPoints.map(p => p.position));
            this.map.fitBounds(bounds, { padding: [20, 20] });
        }

        log(`加载了${this.trackPoints.length}个历史轨迹点`, 'success');
    }

    // 全屏切换
    toggleFullscreen() {
        const mapContainer = document.getElementById(this.containerId).parentElement;
        
        if (!document.fullscreenElement) {
            mapContainer.requestFullscreen().then(() => {
                // 全屏后重新调整地图大小
                setTimeout(() => {
                    this.map.invalidateSize();
                }, 100);
                log('进入全屏模式', 'success');
            });
        } else {
            document.exitFullscreen().then(() => {
                // 退出全屏后重新调整地图大小
                setTimeout(() => {
                    this.map.invalidateSize();
                }, 100);
                log('退出全屏模式', 'success');
            });
        }
    }

    // 显示导航路线 - 使用真实路径规划
    async showNavigationRoute(navigationData) {
        try {
            log('🗺️ 开始显示导航路线:', 'info');
            log(navigationData);

            // 清除之前的导航路线
            this.clearNavigationRoute();

            const { start, end, destination } = navigationData;

            // 🚀 使用OpenRouteService API获取真实路径
            const realRoute = await this.getRealRoute(start, end);

            if (realRoute && realRoute.coordinates) {
                this.showRealNavigationRoute(realRoute, start, end, destination);
            } else {
                // 如果API失败，使用详细路径点（如果有）
                if (navigationData.waypoints && navigationData.waypoints.length > 2) {
                    this.showDetailedNavigationRoute(navigationData);
                } else {
                    // 最后备选：显示直线路径（但标注为直线）
                    this.showStraightLineRoute(start, end, destination);
                }
            }

        } catch (error) {
            log(`❌ 显示导航路线失败: ${error.message}`, 'error');
            // 备选方案：显示详细路径点
            if (navigationData.waypoints && navigationData.waypoints.length > 2) {
                this.showDetailedNavigationRoute(navigationData);
            }
        }
    }

    // 🌐 获取真实路径（使用OpenRouteService API）
    async getRealRoute(start, end) {
        try {
            log('🔍 正在获取真实路径规划...', 'info');

            // OpenRouteService API (免费，无需API key)
            const url = `https://api.openrouteservice.org/v2/directions/driving-car?` +
                       `start=${start.lng},${start.lat}&` +
                       `end=${end.lng},${end.lat}&` +
                       `format=geojson`;

            const response = await fetch(url, {
                headers: {
                    'Accept': 'application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8'
                }
            });

            if (!response.ok) {
                throw new Error(`路径规划API请求失败: ${response.status}`);
            }

            const data = await response.json();

            if (data.features && data.features[0] && data.features[0].geometry) {
                const route = data.features[0];
                const coordinates = route.geometry.coordinates.map(coord => [coord[1], coord[0]]); // 转换为[lat, lng]
                const distance = route.properties.segments[0].distance;
                const duration = route.properties.segments[0].duration;

                log(`✅ 获取真实路径成功 - 距离: ${(distance/1000).toFixed(1)}km, 时间: ${Math.round(duration/60)}分钟`, 'success');

                return {
                    coordinates: coordinates,
                    distance: distance,
                    duration: duration,
                    instructions: route.properties.segments[0].steps || []
                };
            }

            throw new Error('路径数据格式无效');

        } catch (error) {
            log(`⚠️ 路径规划API失败: ${error.message}，使用备选方案`, 'warn');
            return null;
        }
    }

    // 🛣️ 显示真实导航路线
    showRealNavigationRoute(routeData, start, end, destination) {
        try {
            const { coordinates, distance, duration, instructions } = routeData;

            // 绘制真实路径
            this.navigationRoute = L.polyline(coordinates, {
                color: '#FF6B35',
                weight: 6,
                opacity: 0.9,
                smoothFactor: 1
            }).addTo(this.map);

            // 添加路径信息弹窗
            const routeInfo = `
                <div style="text-align: center;">
                    <h4>🛣️ 导航路线</h4>
                    <p><strong>目的地:</strong> ${destination}</p>
                    <p><strong>距离:</strong> ${(distance/1000).toFixed(1)} km</p>
                    <p><strong>预计时间:</strong> ${Math.round(duration/60)} 分钟</p>
                    <p><strong>路径类型:</strong> 遵守交通规则</p>
                </div>
            `;
            this.navigationRoute.bindPopup(routeInfo);

            // 添加起点标记
            const startMarker = L.marker([start.lat, start.lng], {
                icon: L.divIcon({
                    className: 'navigation-marker start-marker',
                    html: '<div class="marker-content">🚗</div>',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                })
            }).addTo(this.map);
            startMarker.bindPopup(`
                <div>
                    <h4>🚗 起点</h4>
                    <p>当前位置</p>
                    <p>${start.lat.toFixed(6)}°N, ${start.lng.toFixed(6)}°E</p>
                </div>
            `);
            this.navigationMarkers.push(startMarker);

            // 添加终点标记
            const endMarker = L.marker([end.lat, end.lng], {
                icon: L.divIcon({
                    className: 'navigation-marker end-marker',
                    html: '<div class="marker-content">🏢</div>',
                    iconSize: [30, 30],
                    iconAnchor: [15, 15]
                })
            }).addTo(this.map);
            endMarker.bindPopup(`
                <div>
                    <h4>🏢 终点</h4>
                    <p>${destination}</p>
                    <p>${end.lat.toFixed(6)}°N, ${end.lng.toFixed(6)}°E</p>
                    <p><strong>距离:</strong> ${(distance/1000).toFixed(1)} km</p>
                    <p><strong>时间:</strong> ${Math.round(duration/60)} 分钟</p>
                </div>
            `);
            this.navigationMarkers.push(endMarker);

            // 添加关键转弯点标记
            if (instructions && instructions.length > 0) {
                this.addTurnInstructions(instructions, coordinates);
            }

            // 调整地图视图
            this.map.fitBounds(this.navigationRoute.getBounds(), {
                padding: [50, 50]
            });

            this.isNavigating = true;
            log(`✅ 真实导航路线显示完成 - ${(distance/1000).toFixed(1)}km, ${Math.round(duration/60)}分钟`, 'success');

        } catch (error) {
            log(`❌ 显示真实导航路线失败: ${error.message}`, 'error');
        }
    }

    // 🧭 添加转弯指示标记
    addTurnInstructions(instructions, coordinates) {
        try {
            instructions.forEach((instruction, index) => {
                if (index === 0 || index === instructions.length - 1) return; // 跳过起点和终点

                const stepIndex = Math.min(instruction.way_points[0], coordinates.length - 1);
                const position = coordinates[stepIndex];

                if (!position) return;

                // 获取转弯类型图标
                const turnIcon = this.getTurnIcon(instruction.instruction);

                const turnMarker = L.marker(position, {
                    icon: L.divIcon({
                        className: 'turn-instruction-marker',
                        html: `<div class="turn-content">${turnIcon}</div>`,
                        iconSize: [24, 24],
                        iconAnchor: [12, 12]
                    })
                }).addTo(this.map);

                turnMarker.bindPopup(`
                    <div style="text-align: center;">
                        <h5>${turnIcon} 转弯指示</h5>
                        <p>${instruction.instruction}</p>
                        <p><strong>距离:</strong> ${instruction.distance}m</p>
                    </div>
                `);

                this.navigationMarkers.push(turnMarker);
            });

            log(`添加了${instructions.length}个转弯指示`, 'info');
        } catch (error) {
            log(`添加转弯指示失败: ${error.message}`, 'warn');
        }
    }

    // 🔄 获取转弯图标
    getTurnIcon(instruction) {
        const text = instruction.toLowerCase();
        if (text.includes('left') || text.includes('左转')) return '⬅️';
        if (text.includes('right') || text.includes('右转')) return '➡️';
        if (text.includes('straight') || text.includes('直行')) return '⬆️';
        if (text.includes('u-turn') || text.includes('掉头')) return '🔄';
        if (text.includes('exit') || text.includes('出口')) return '🛤️';
        return '📍';
    }

    // 📏 显示直线路径（备选方案）
    showStraightLineRoute(start, end, destination) {
        try {
            log('⚠️ 使用直线路径（备选方案）', 'warn');

            const routePoints = [
                [start.lat, start.lng],
                [end.lat, end.lng]
            ];

            // 计算直线距离
            const distance = this.calculateDistance(start.lat, start.lng, end.lat, end.lng);

            // 绘制直线路径（虚线表示非真实路径）
            this.navigationRoute = L.polyline(routePoints, {
                color: '#FF6B35',
                weight: 5,
                opacity: 0.7,
                dashArray: '20, 10'
            }).addTo(this.map);

            // 添加警告信息
            this.navigationRoute.bindPopup(`
                <div style="text-align: center;">
                    <h4>⚠️ 直线路径</h4>
                    <p><strong>目的地:</strong> ${destination}</p>
                    <p><strong>直线距离:</strong> ${(distance/1000).toFixed(1)} km</p>
                    <p style="color: orange;"><strong>注意:</strong> 这是直线距离，不遵循道路</p>
                    <p>请使用专业导航应用获取真实路径</p>
                </div>
            `);

            this.addBasicMarkers(start, end, destination, distance);

            // 调整地图视图
            this.map.fitBounds(this.navigationRoute.getBounds(), {
                padding: [50, 50]
            });

            this.isNavigating = true;
            log(`直线路径显示完成 - ${(distance/1000).toFixed(1)}km`, 'warn');

        } catch (error) {
            log(`显示直线路径失败: ${error.message}`, 'error');
        }
    }

    // 📍 添加基础标记
    addBasicMarkers(start, end, destination, distance) {
        // 起点标记
        const startMarker = L.marker([start.lat, start.lng], {
            icon: L.divIcon({
                className: 'navigation-marker start-marker',
                html: '<div class="marker-content">🚗</div>',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(this.map);
        startMarker.bindPopup('起点：当前位置');
        this.navigationMarkers.push(startMarker);

        // 终点标记
        const endMarker = L.marker([end.lat, end.lng], {
            icon: L.divIcon({
                className: 'navigation-marker end-marker',
                html: '<div class="marker-content">🏢</div>',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(this.map);
        endMarker.bindPopup(`终点: ${destination}<br>直线距离: ${(distance/1000).toFixed(1)}km`);
        this.navigationMarkers.push(endMarker);
    }

    // 📐 计算两点间距离（Haversine公式）
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371000; // 地球半径（米）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    // 显示详细导航路线（多个路径点）
    showDetailedNavigationRoute(navigationData) {
        try {
            log('显示详细导航路线:', 'info');
            const { waypoints, destination, total_distance } = navigationData;

            // 构建路径点数组
            const routePoints = waypoints.map(point => [point.lat, point.lng]);

            // 绘制主导航路线
            this.navigationRoute = L.polyline(routePoints, {
                color: '#FF6B35',
                weight: 6,
                opacity: 0.9,
                dashArray: '15, 10'
            }).addTo(this.map);

            // 添加路径点标记
            waypoints.forEach((waypoint, index) => {
                let markerIcon, markerClass, popupContent;

                if (index === 0) {
                    // 起点
                    markerIcon = '🚗';
                    markerClass = 'navigation-marker start-marker';
                    popupContent = `起点: ${waypoint.instruction}`;
                } else if (index === waypoints.length - 1) {
                    // 终点
                    markerIcon = '🏢';
                    markerClass = 'navigation-marker end-marker';
                    popupContent = `终点: ${waypoint.instruction}`;
                } else {
                    // 中间路径点
                    markerIcon = `${index}`;
                    markerClass = 'navigation-marker waypoint-marker';
                    popupContent = `路径点${index}: ${waypoint.instruction}`;
                }

                const marker = L.marker([waypoint.lat, waypoint.lng], {
                    icon: L.divIcon({
                        className: markerClass,
                        html: `<div class="marker-content">${markerIcon}</div>`,
                        iconSize: [35, 35],
                        iconAnchor: [17, 17]
                    })
                }).addTo(this.map);

                marker.bindPopup(popupContent);
                this.navigationMarkers.push(marker);
            });

            // 调整地图视图
            this.map.fitBounds(this.navigationRoute.getBounds(), {
                padding: [50, 50]
            });

            this.isNavigating = true;
            log(`详细导航路线显示完成 - 总距离: ${total_distance}米，路径点: ${waypoints.length}个`, 'success');

        } catch (error) {
            log(`显示详细导航路线失败: ${error.message}`, 'error');
        }
    }

    // 清除导航路线
    clearNavigationRoute() {
        if (this.navigationRoute) {
            this.map.removeLayer(this.navigationRoute);
            this.navigationRoute = null;
        }

        this.navigationMarkers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.navigationMarkers = [];

        this.isNavigating = false;
        log('导航路线已清除', 'info');
    }

    // 获取当前位置
    getCurrentPosition() {
        if (this.currentMarker) {
            const position = this.currentMarker.getLatLng();
            return {
                lat: position.lat,
                lng: position.lng
            };
        }
        return null;
    }

    // 获取Leaflet地图实例（用于路径规划）
    get leafletMap() {
        return this.map;
    }

    // 获取地图实例
    getMap() {
        return this.map;
    }
}

// 导出地图类
window.GPSMap = GPSMap;
