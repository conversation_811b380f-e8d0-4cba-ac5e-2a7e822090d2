/**
 * LCD显示测试程序
 * 用于测试中文字符和小数点显示
 */

#include "lcd_test.h"
#include "lcd_display_hal.h"

/**
 * @brief 测试小数点和数字显示
 */
void LCD_Test_Numbers(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 测试标题
    LCD_ShowString(10, 10, (const uint8_t*)"Number Display Test", WHITE, BLACK, 16, 0);
    
    // 测试小数点显示
    LCD_ShowString(10, 40, (const uint8_t*)"Float: 3.78", CYAN, BLACK, 16, 0);
    LCD_ShowFloatNum1(10, 60, 3.78f, 4, <PERSON>YAN, BLACK, 16);
    
    // 测试各种符号
    LCD_ShowString(10, 90, (const uint8_t*)"Symbols: . : - /", YELLOW, BLACK, 16, 0);
    
    // 测试数字
    LCD_ShowString(10, 120, (const uint8_t*)"Numbers: 0123456789", GREEN, BLACK, 16, 0);
    
    // 测试字母
    LCD_ShowString(10, 150, (const uint8_t*)"Letters: ABCDEFG", RED, BLACK, 16, 0);
    LCD_ShowString(10, 170, (const uint8_t*)"letters: abcdefg", RED, BLACK, 16, 0);
}

/**
 * @brief 测试导航显示
 */
void LCD_Test_Navigation(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 标题
    LCD_ShowString(10, 10, (const uint8_t*)"Navigation Test", WHITE, BLACK, 16, 0);
    
    // 距离和时间
    LCD_ShowString(10, 40, (const uint8_t*)"3.78km", CYAN, BLACK, 16, 0);
    LCD_ShowString(120, 40, (const uint8_t*)"4min", CYAN, BLACK, 16, 0);
    LCD_ShowString(200, 40, (const uint8_t*)"OSRM", GREEN, BLACK, 12, 0);
    
    // 分隔线
    LCD_DrawLine(10, 65, 230, 65, WHITE);
    
    // 导航步骤
    uint16_t y = 80;
    
    // 步骤1
    Draw_Circle(20, y + 12, 10, GREEN);
    LCD_ShowString(17, y + 8, (const uint8_t*)"1", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 4, (const uint8_t*)"Start from Lingtai Rd", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 18, (const uint8_t*)"1.1km", CYAN, BLACK, 12, 0);
    // 直行箭头
    LCD_DrawLine(206, y + 4, 202, y + 8, YELLOW);
    LCD_DrawLine(206, y + 4, 210, y + 8, YELLOW);
    LCD_DrawLine(206, y + 4, 206, y + 16, YELLOW);
    y += 35;
    
    // 连接线
    LCD_DrawLine(20, y - 12, 20, y + 5, CYAN);
    
    // 步骤2
    Draw_Circle(20, y + 12, 10, GREEN);
    LCD_ShowString(17, y + 8, (const uint8_t*)"2", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 4, (const uint8_t*)"Turn left to Hengzhou Ave", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 18, (const uint8_t*)"2.0km", CYAN, BLACK, 12, 0);
    // 左转箭头
    LCD_DrawLine(200, y + 10, 208, y + 6, YELLOW);
    LCD_DrawLine(200, y + 10, 208, y + 14, YELLOW);
    LCD_DrawLine(200, y + 10, 212, y + 10, YELLOW);
    y += 35;
    
    // 连接线
    LCD_DrawLine(20, y - 12, 20, y + 5, CYAN);
    
    // 步骤3
    Draw_Circle(20, y + 12, 10, GREEN);
    LCD_ShowString(17, y + 8, (const uint8_t*)"3", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 4, (const uint8_t*)"Continue on Hengzhou Ave", WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y + 18, (const uint8_t*)"601m", CYAN, BLACK, 12, 0);
    // 直行箭头
    LCD_DrawLine(206, y + 4, 202, y + 8, YELLOW);
    LCD_DrawLine(206, y + 4, 210, y + 8, YELLOW);
    LCD_DrawLine(206, y + 4, 206, y + 16, YELLOW);
}

/**
 * @brief 测试中文字符显示（简化版）
 */
void LCD_Test_Chinese(void)
{
    // 清屏
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 标题
    LCD_ShowString(10, 10, (const uint8_t*)"Chinese Character Test", WHITE, BLACK, 16, 0);
    
    uint16_t y = 50;
    uint16_t x = 10;
    
    // 测试常用导航中文字符
    const char* chinese_chars[] = {
        "公", "里", "米", "分", "钟",
        "从", "路", "出", "发", "左",
        "转", "进", "入", "继", "续",
        "沿", "直", "行", "右", "到",
        "达", "目", "的", "地"
    };
    
    for(int i = 0; i < 24; i++) {
        LCD_ShowChinese(x, y, (uint8_t*)chinese_chars[i], WHITE, BLACK, 16, 0);
        x += 20;
        if(x > 200) {
            x = 10;
            y += 25;
        }
    }
    
    // 显示说明
    LCD_ShowString(10, y + 30, (const uint8_t*)"Simplified Chinese patterns", GRAY, BLACK, 12, 0);
}

/**
 * @brief LCD测试主函数
 */
void LCD_Test_All(void)
{
    static uint8_t test_mode = 0;
    static uint32_t last_switch_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每3秒切换一次测试模式
    if(current_time - last_switch_time > 3000) {
        last_switch_time = current_time;
        test_mode = (test_mode + 1) % 3;
        
        switch(test_mode) {
            case 0:
                LCD_Test_Numbers();
                break;
            case 1:
                LCD_Test_Navigation();
                break;
            case 2:
                LCD_Test_Chinese();
                break;
        }
    }
}
