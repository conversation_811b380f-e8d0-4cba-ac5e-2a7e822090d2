#include "uart2_driver.h"

// 串口2 DMA 接收缓冲区
uint8_t uart2_rx_dma_buffer[UART2_BUFFER_SIZE]; 

// 串口2 环形缓冲区对应的线性数组
uint8_t uart2_ring_buffer_input[UART2_BUFFER_SIZE]; 
// 串口2 环形缓冲区
struct rt_ringbuffer uart2_ring_buffer; 

// 串口2 数据处理缓冲区
uint8_t uart2_data_buffer[UART2_BUFFER_SIZE]; 

/**
 * @brief 串口2格式化输出函数
 * @param huart UART句柄
 * @param format 格式化字符串
 * @return 输出的字符数
 */
int Uart2_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512]; // 临时存储格式化后的字符串
    va_list arg;      // 可变参数列表
    int len;          // 输出字符串长度

    va_start(arg, format);
    // 安全地格式化字符串到 buffer
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // 添加发送调试信息
    extern UART_HandleTypeDef huart1;
    extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
    my_printf(&huart1, "[TX] Sending to ESP-01: %s", buffer);

    // 通过 HAL 库发送 buffer 中的数据
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}

/**
 * @brief 串口2接收完成回调函数
 * @param huart UART句柄
 * @param Size 接收到的数据大小
 */
void HAL_UARTEx_RxEventCallback_UART2(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 确保是目标串口 (USART2)
    if (huart->Instance == USART2)
    {
        // 停止当前DMA传输
        HAL_UART_DMAStop(huart);

        // 将接收到的数据放入环形缓冲区
        rt_ringbuffer_put(&uart2_ring_buffer, uart2_rx_dma_buffer, Size);

        // 清空DMA缓冲区
        memset(uart2_rx_dma_buffer, 0, sizeof(uart2_rx_dma_buffer));

        // 关键修复：重置UART和DMA状态
        huart->RxState = HAL_UART_STATE_READY;
        huart->hdmarx->State = HAL_DMA_STATE_READY;

        // 重新启动DMA接收
        HAL_UARTEx_ReceiveToIdle_DMA(&huart2, uart2_rx_dma_buffer, sizeof(uart2_rx_dma_buffer));

        // 关闭半满中断
        __HAL_DMA_DISABLE_IT(&hdma_usart2_rx, DMA_IT_HT);
    }
}
