# LCD硬件连接验证指南

## 你的当前连接
- GND → 外部电源GND ✅
- VCC → 外部电源3.3V ✅
- SCL → STM32F407VET6的PB13 ✅
- SDA → STM32F407VET6的PB15 ✅
- RES → STM32F407VET6的PD4 ✅
- DC → STM32F407VET6的PD0 ✅
- CS → **未连接** ⚠️
- BLK → STM32F407VET6的PD1 ✅

## 关键问题分析

### 1. CS片选信号缺失
**原始例程中CS的作用：**
- 原始例程：CS = PD1
- 每次SPI通信前拉低CS，通信后拉高CS
- 这是标准SPI协议的片选信号

**你的情况：**
- CS未连接，但PD1用作背光控制
- 这可能导致SPI通信失败

### 2. 可能的解决方案

#### 方案A：连接CS信号（推荐）
1. 将LCD的CS引脚连接到STM32的一个空闲GPIO（如PD2）
2. 修改代码添加CS控制

#### 方案B：软件忽略CS（当前尝试）
1. 有些LCD模块CS可以接地或接VCC固定
2. 修改软件跳过CS控制

## 测试步骤

### 第一步：背光测试
运行程序后观察：
- [ ] LCD背光有明暗变化
- [ ] 背光闪烁10次
- [ ] 完全没有变化

### 第二步：根据背光测试结果判断

#### 如果背光正常闪烁
说明：
- 电源连接正确
- BLK引脚连接正确
- STM32工作正常

继续检查SPI通信问题。

#### 如果背光完全没有变化
可能原因：
1. **BLK连接错误**：检查是否真的连接到PD1
2. **电源问题**：检查3.3V供电是否正常
3. **LCD模块问题**：LCD模块可能损坏
4. **接触不良**：检查所有连接线

### 第三步：电源和连接检查

#### 用万用表检查：
1. **电源电压**：LCD的VCC引脚应该有3.3V
2. **GND连接**：LCD的GND与STM32的GND应该导通
3. **引脚连接**：
   - LCD的BLK与STM32的PD1应该导通
   - LCD的SCL与STM32的PB13应该导通
   - LCD的SDA与STM32的PB15应该导通
   - LCD的RES与STM32的PD4应该导通
   - LCD的DC与STM32的PD0应该导通

### 第四步：CS信号处理

#### 临时测试方法：
1. 将LCD的CS引脚直接接GND（永久选中）
2. 或者将CS引脚接VCC（永久不选中，但有些LCD支持）

#### 正确方法：
1. 将CS连接到STM32的空闲GPIO（如PD2、PD3等）
2. 修改代码添加CS控制

## 常见问题排查

### 问题1：背光不闪烁
- 检查BLK是否连接到PD1
- 检查3.3V供电
- 尝试将BLK直接连接到3.3V看是否常亮

### 问题2：背光正常但屏幕仍是白屏
- CS信号问题（最可能）
- SPI时序问题
- LCD驱动IC不匹配

### 问题3：屏幕有变化但显示异常
- 像素格式问题
- 显示方向问题
- 初始化序列问题

## 下一步行动

1. **立即测试**：编译运行程序，观察背光是否闪烁
2. **根据结果**：
   - 背光正常 → 处理CS信号问题
   - 背光异常 → 检查硬件连接和供电
3. **反馈结果**：告诉我具体观察到的现象
