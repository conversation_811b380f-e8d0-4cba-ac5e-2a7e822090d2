@echo off
echo Starting compilation...
cd /d "%~dp0"
echo Current directory: %CD%
if exist "C:\Keil_v5\UV4\UV4.exe" (
    echo Found Keil at C:\Keil_v5\UV4\UV4.exe
    "C:\Keil_v5\UV4\UV4.exe" -b <PERSON>_Xifeng_F4.uvprojx
) else if exist "C:\Keil\UV4\UV4.exe" (
    echo Found Keil at C:\Keil\UV4\UV4.exe
    "C:\Keil\UV4\UV4.exe" -b Car_Xifeng_F4.uvprojx
) else (
    echo Keil not found. Please check installation path.
    echo Looking for UV4.exe in common locations...
    dir "C:\Keil*\UV4\UV4.exe" /s 2>nul
)
echo Compilation finished.
pause
