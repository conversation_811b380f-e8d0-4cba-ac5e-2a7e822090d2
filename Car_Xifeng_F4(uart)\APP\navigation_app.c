#include "navigation_app.h"
#include "esp01_app.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 目的地数据库 - 精简版
static const Destination_t destinations[20] = {
    {"wangda", 26.8892785f, 112.6609182f, "酃湖万达广场"},
    {"wanda", 26.8892785f, 112.6609182f, "酃湖万达广场"},
    {"shuyuan", 26.8850f, 112.6700f, "酃湖书院"},
    {"tiyuzhonxin", 26.8900796f, 112.6741752f, "衡阳市体育中心"},
    {"gaotie", 26.8945f, 112.6123f, "衡阳东高铁站"},
    {"daxue", 26.8812f, 112.6769f, "衡阳师范学院"},
    {"yiyuan", 26.9043654f, 112.5962734f, "南华大学附属第一医院"},
    {"gongyuan", 26.8934f, 112.5967f, "石鼓公园"},
    {"shangchang", 26.8823f, 112.6145f, "步步高购物中心"},
    {"jichang", 26.7345f, 112.6789f, "衡阳南岳机场"},
    {"xuexiao", 26.8812f, 112.6769f, "衡阳师范学院(校区)"},
    {"zhongxin", 26.8912f, 112.6034f, "衡阳市中心"},
    {"huochezhan", 26.8934986f, 112.6260051f, "衡阳火车站"}
};

NavigationState_t nav_state = NAV_STATE_IDLE;
Navigation_t current_navigation;

void Navigation_Init(void)
{
    nav_state = NAV_STATE_IDLE;
    memset(&current_navigation, 0, sizeof(Navigation_t));
    my_printf(&huart1, "导航系统已初始化，输入 nav_help 查看帮助\r\n");
}

void Navigation_Task(void)
{
    switch (nav_state) {
        case NAV_STATE_NAVIGATING:
            // 简化的导航任务处理
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "已到达目的地: %s\r\n", current_navigation.destination.description);
            nav_state = NAV_STATE_IDLE;
            break;
        default:
            break;
    }
}

uint8_t Navigation_StartNavigation(const char* destination_name)
{
    if (nav_state != NAV_STATE_IDLE) {
        my_printf(&huart1, "停止当前导航，开始新导航\r\n");
        Navigation_StopNavigation();
        HAL_Delay(100);
    }

    my_printf(&huart1, "正在查找目的地: %s\r\n", destination_name);

    Destination_t dest;
    if (!Navigation_FindDestination(destination_name, &dest)) {
        my_printf(&huart1, "未找到目的地: %s\r\n", destination_name);
        return 0;
    }

    my_printf(&huart1, "找到目的地: %s\r\n", dest.description);

    // 获取GPS数据
    extern void esp01_GetRealLocation(float *lat, float *lon, float *alt);
    float current_lat, current_lon, current_alt;
    esp01_GetRealLocation(&current_lat, &current_lon, &current_alt);

    my_printf(&huart1, "当前GPS坐标: %.6f°N, %.6f°E\r\n", current_lat, current_lon);

    if (current_lat == 0.0f || current_lon == 0.0f) {
        my_printf(&huart1, "GPS信号无效，无法开始导航\r\n");
        return 0;
    }

    current_navigation.destination = dest;
    current_navigation.is_active = 1;
    current_navigation.is_arrived = 0;

    // 简化的路径规划
    Navigation_PlanRoute(current_lat, current_lon, dest.latitude, dest.longitude);
    nav_state = NAV_STATE_NAVIGATING;

    my_printf(&huart1, "开始导航到: %s\r\n", dest.description);
    my_printf(&huart1, "总距离: %.0f米\r\n", current_navigation.total_distance);

    // 上传导航命令
    extern void esp01_UploadNavigationCommand(int field3_value);
    int field3_value = 9999; // 默认万达
    if (strcmp(dest.name, "wangda") == 0 || strcmp(dest.name, "wanda") == 0) {
        field3_value = 1111;
    } else if (strcmp(dest.name, "shuyuan") == 0) {
        field3_value = 2222;
    } else if (strcmp(dest.name, "tiyuzhonxin") == 0) {
        field3_value = 3333;
    } else if (strcmp(dest.name, "huochezhan") == 0) {
        field3_value = 4444;
    } else if (strcmp(dest.name, "yiyuan") == 0) {
        field3_value = 5555;
    } else if (strcmp(dest.name, "daxue") == 0 || strcmp(dest.name, "xuexiao") == 0) {
        field3_value = 6666;
    }

    my_printf(&huart1, "直接上传导航命令: field3=%d (%s)\r\n", field3_value, dest.description);
    esp01_UploadNavigationCommand(field3_value);

    return 1;
}

void Navigation_StopNavigation(void)
{
    nav_state = NAV_STATE_IDLE;
    current_navigation.is_active = 0;
    my_printf(&huart1, "导航已停止\r\n");
}

void Navigation_ProcessCommand(const char* command)
{
    if (strncmp(command, "nav_", 4) == 0) {
        const char* nav_cmd = command + 4;
        
        if (strcmp(nav_cmd, "help") == 0) {
            my_printf(&huart1, "导航系统帮助:\r\n");
            my_printf(&huart1, "nav_help - 显示帮助\r\n");
            my_printf(&huart1, "nav_list - 显示目的地列表\r\n");
            my_printf(&huart1, "nav_status - 显示导航状态\r\n");
            my_printf(&huart1, "nav_stop - 停止导航\r\n");
            my_printf(&huart1, "wangda - 导航到万达广场\r\n");
            my_printf(&huart1, "gaotie - 导航到高铁站\r\n");
        }
        else if (strcmp(nav_cmd, "list") == 0) {
            Navigation_PrintDestinations();
        }
        else if (strcmp(nav_cmd, "status") == 0) {
            Navigation_PrintStatus();
        }
        else if (strcmp(nav_cmd, "stop") == 0) {
            Navigation_StopNavigation();
        }
        else if (strcmp(nav_cmd, "test1") == 0) {
            my_printf(&huart1, "测试万达广场导航 (field3=1111)\r\n");
            Navigation_StartNavigation("wangda");
        }
        else if (strcmp(nav_cmd, "test2") == 0) {
            my_printf(&huart1, "测试酃湖书院导航 (field3=2222)\r\n");
            Navigation_StartNavigation("shuyuan");
        }
        else if (strcmp(nav_cmd, "test3") == 0) {
            my_printf(&huart1, "测试体育中心导航 (field3=3333)\r\n");
            Navigation_StartNavigation("tiyuzhonxin");
        }
        else if (strcmp(nav_cmd, "test4") == 0) {
            my_printf(&huart1, "测试火车站导航 (field3=4444)\r\n");
            Navigation_StartNavigation("huochezhan");
        }
        else if (strcmp(nav_cmd, "test5") == 0) {
            my_printf(&huart1, "测试医院导航 (field3=5555)\r\n");
            Navigation_StartNavigation("yiyuan");
        }
        else if (strcmp(nav_cmd, "test6") == 0) {
            my_printf(&huart1, "测试衡阳师范学院导航 (field3=6666)\r\n");
            Navigation_StartNavigation("daxue");
        }
    }
    else {
        // 检查是否为数字编号
        if (strcmp(command, "1") == 0) {
            my_printf(&huart1, "数字导航: 万达广场 (1)\r\n");
            Navigation_StartNavigation("wangda");
            return;
        }
        else if (strcmp(command, "2") == 0) {
            my_printf(&huart1, "数字导航: 酃湖书院 (2)\r\n");
            Navigation_StartNavigation("shuyuan");
            return;
        }
        else if (strcmp(command, "3") == 0) {
            my_printf(&huart1, "数字导航: 体育中心 (3)\r\n");
            Navigation_StartNavigation("tiyuzhonxin");
            return;
        }
        else if (strcmp(command, "4") == 0) {
            my_printf(&huart1, "数字导航: 火车站 (4)\r\n");
            Navigation_StartNavigation("huochezhan");
            return;
        }
        else if (strcmp(command, "5") == 0) {
            my_printf(&huart1, "数字导航: 医院 (5)\r\n");
            Navigation_StartNavigation("yiyuan");
            return;
        }
        else if (strcmp(command, "6") == 0) {
            my_printf(&huart1, "数字导航: 衡阳师范学院 (6)\r\n");
            Navigation_StartNavigation("daxue");
            return;
        }

        // 检查是否为目的地名称
        uint8_t found = 0;
        for (int i = 0; i < 20; i++) {
            if (strcmp(command, destinations[i].name) == 0) {
                Navigation_StartNavigation(destinations[i].name);
                found = 1;
                break;
            }
        }

        if (!found && strlen(command) > 0) {
            my_printf(&huart1, "未知命令: %s\r\n", command);
            my_printf(&huart1, "输入 nav_help 查看帮助\r\n");
        }
    }
}

uint8_t Navigation_FindDestination(const char* name, Destination_t* dest)
{
    for (int i = 0; i < 20; i++) {
        if (strcmp(name, destinations[i].name) == 0) {
            *dest = destinations[i];
            return 1;
        }
    }
    return 0;
}

void Navigation_PlanRoute(float start_lat, float start_lon, float end_lat, float end_lon)
{
    // 简化的路径规划
    current_navigation.waypoint_count = 2;
    
    current_navigation.waypoints[0].latitude = start_lat;
    current_navigation.waypoints[0].longitude = start_lon;
    strcpy(current_navigation.waypoints[0].instruction, "从当前位置出发");

    current_navigation.waypoints[1].latitude = end_lat;
    current_navigation.waypoints[1].longitude = end_lon;
    strcpy(current_navigation.waypoints[1].instruction, "到达目的地");
    
    float distance = Navigation_CalculateDistance(start_lat, start_lon, end_lat, end_lon);
    current_navigation.total_distance = distance;
    current_navigation.remaining_distance = distance;
    
    float bearing = Navigation_CalculateBearing(start_lat, start_lon, end_lat, end_lon);
    
    const char* direction = "";
    if (bearing >= 337.5f || bearing < 22.5f) direction = "北";
    else if (bearing >= 22.5f && bearing < 67.5f) direction = "东北";
    else if (bearing >= 67.5f && bearing < 112.5f) direction = "东";
    else if (bearing >= 112.5f && bearing < 157.5f) direction = "东南";
    else if (bearing >= 157.5f && bearing < 202.5f) direction = "南";
    else if (bearing >= 202.5f && bearing < 247.5f) direction = "西南";
    else if (bearing >= 247.5f && bearing < 292.5f) direction = "西";
    else direction = "西北";

    snprintf(current_navigation.waypoints[0].instruction, 128,
             "向%s方向行驶 %.0f米", direction, distance);
}

float Navigation_CalculateDistance(float lat1, float lon1, float lat2, float lon2)
{
    const float R = 6371000; // 地球半径（米）
    float dLat = (lat2 - lat1) * M_PI / 180.0f;
    float dLon = (lon2 - lon1) * M_PI / 180.0f;
    float a = sin(dLat/2) * sin(dLat/2) + cos(lat1 * M_PI / 180.0f) * cos(lat2 * M_PI / 180.0f) * sin(dLon/2) * sin(dLon/2);
    float c = 2 * atan2(sqrt(a), sqrt(1-a));
    return R * c;
}

float Navigation_CalculateBearing(float lat1, float lon1, float lat2, float lon2)
{
    float dLon = (lon2 - lon1) * M_PI / 180.0f;
    float lat1_rad = lat1 * M_PI / 180.0f;
    float lat2_rad = lat2 * M_PI / 180.0f;
    
    float y = sin(dLon) * cos(lat2_rad);
    float x = cos(lat1_rad) * sin(lat2_rad) - sin(lat1_rad) * cos(lat2_rad) * cos(dLon);
    
    float bearing = atan2(y, x) * 180.0f / M_PI;
    return fmod(bearing + 360.0f, 360.0f);
}

void Navigation_PrintStatus(void)
{
    my_printf(&huart1, "导航状态:\r\n");
    switch (nav_state) {
        case NAV_STATE_IDLE:
            my_printf(&huart1, "状态: 空闲\r\n");
            break;
        case NAV_STATE_NAVIGATING:
            my_printf(&huart1, "状态: 导航中\r\n");
            my_printf(&huart1, "目的地: %s\r\n", current_navigation.destination.description);
            my_printf(&huart1, "剩余距离: %.0f米\r\n", current_navigation.remaining_distance);
            break;
        case NAV_STATE_ARRIVED:
            my_printf(&huart1, "状态: 已到达\r\n");
            break;
        default:
            my_printf(&huart1, "状态: 未知\r\n");
            break;
    }
}

void Navigation_PrintDestinations(void)
{
    my_printf(&huart1, "可用目的地:\r\n");
    for (int i = 0; i < 20 && destinations[i].name[0] != '\0'; i++) {
        my_printf(&huart1, "%s - %s\r\n", destinations[i].name, destinations[i].description);
    }
}
