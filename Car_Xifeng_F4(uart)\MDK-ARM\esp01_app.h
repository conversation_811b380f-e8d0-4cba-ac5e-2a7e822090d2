/**
 * ESP01 WiFi模块应用程序头文件
 *
 * Author: AI Assistant
 * Version: 4.0 - 全新架构
 * Date: 2025-01-27
 */

#ifndef __ESP01_APP_H
#define __ESP01_APP_H

#include "MyDefine.h"

// 配置常量
#define GPS_UPLOAD_INTERVAL 10000  // GPS上传间隔（毫秒）

// ESP01状态枚举
typedef enum {
    ESP01_STATE_IDLE = 0,
    ESP01_STATE_INIT,
    ESP01_STATE_CONNECTING,
    ESP01_STATE_CONNECTED,
    ESP01_STATE_ERROR
} ESP01_State_t;

// 函数声明
void esp01_Init(void);
void esp01_InitSequence(void);
uint8_t esp01_TestServerConnection(void);
void esp01_UploadGPSData(void);
void esp01_SimpleUploadGPS(void);  // 简化的GPS上传函数
void esp01_Task(void);
ESP01_State_t esp01_GetState(void);
void esp01_CheckConnection(void);
void esp01_GetRealLocation(float *lat, float *lon, float *alt);
void esp01_SetSimulationLocation(float lat, float lon);  // 设置模拟GPS坐标
void esp01_SwitchToNextServer(void);
void esp01_Reset(void);
void esp01_ReconnectWiFi(void);  // 手动重新连接WiFi
void esp01_CheckAndReinit(void); // ESP01状态检查和重新初始化

// 兼容性函数
void esp01_SendLocationData(void);
void esp01_SendNavigationData(const char* route_data);
void esp01_UploadNavigationCommand(int field3_value);
uint8_t esp01_DownloadNavigationData(char* response_buffer, uint16_t buffer_size);
uint8_t esp01_ReadResponse(char* buffer, uint16_t buffer_size, uint32_t timeout_ms);  // 简单直接上传导航命令
uint8_t esp01_EstablishTCPConnection(void);
void esp01_NetworkDiagnostic(void);
uint8_t esp01_QuickTest(void);
uint8_t esp01_QuickConnectionTest(void);  // 新增快速连接测试
void esp01_DebugMode(void);               // 新增调试模式
void esp01_SetConnected(void);

// 旧版本兼容函数
void esp01_StartInit(void);
void esp01_NetworkDiagnostics(void);
void esp01_ForceReset(void);
uint8_t esp01_TryTCPWithIP(void);
void esp01_SetTCPConnected(void);
void esp01_SetDataSendReady(void);
void esp01_ResetTCPState(void);
void esp01_SendNavigationData(const char* route_data);

// 新增：多目的地导航支持函数
int esp01_GetDestinationCode(const char* route_data);
const char* esp01_GetDestinationName(int destination_code);

#endif /* __ESP01_APP_H */