# WANDA地图显示使用说明

## 🎉 恭喜！LCD显示成功！

你的LCD现在已经可以正常显示了，包括：
- ✅ 背光控制正常
- ✅ 彩色文字显示正常
- ✅ 图形绘制功能正常

## 🗺️ WANDA地图显示功能

现在我为你添加了完整的LCD地图显示功能，可以在240x320的LCD屏幕上显示：

### 📱 显示内容
1. **地图区域** (220x200像素)
   - 网格线显示
   - 当前位置标记 (红色圆点)
   - 目的地标记 (绿色方块)
   - 导航路径线 (蓝色直线)

2. **导航信息**
   - 到目的地距离 (米)
   - 方位角度 (度)
   - 实时位置更新

3. **标题栏**
   - "WANDA Navigation Map"

### 🚀 功能特点

#### 1. 自动启动
- LCD初始化完成后，会自动启动地图显示
- 默认显示衡阳地区地图

#### 2. 实时更新
- 每500ms更新一次地图显示
- 模拟GPS位置移动演示

#### 3. 导航集成
- 与现有导航系统完全集成
- 支持WANDA检测器命令
- 自动设置目的地和路径

### 🎯 预设目的地

地图系统包含以下预设目的地：
- 🏬 万达广场 (26.8892785°N, 112.6609182°E)
- 📚 酃湖书院 (26.8850°N, 112.6700°E)  
- 🏟️ 体育中心 (26.8900796°N, 112.6741752°E)
- 🚄 衡阳东站 (26.8945°N, 112.6123°E)
- 🏫 师范学院 (26.8812°N, 112.6769°E)
- 🏥 附一医院 (26.9043654°N, 112.5962734°E)

### 📋 使用方法

#### 方法1：自动演示模式
1. 编译并下载程序到STM32F407VET6
2. 程序会自动启动地图显示
3. 观察LCD屏幕上的地图和导航信息
4. 位置会自动模拟移动进行演示

#### 方法2：与WANDA检测器联动
1. 使用串口发送WANDA命令
2. 系统会自动解析GPS坐标
3. 地图会更新当前位置和目的地
4. 显示实时导航信息

#### 方法3：手动控制
可以通过以下函数手动控制地图：
```c
// 更新当前位置
tft_UpdateMapPosition(26.8812f, 112.6769f);

// 设置目的地
tft_SetMapDestination("万达广场", 26.8892785f, 112.6609182f);
```

### 🔧 技术实现

#### 坐标转换
- GPS坐标 → 屏幕像素坐标
- 支持多种缩放级别
- 简化的墨卡托投影

#### 地图元素
- **当前位置**：红色圆点 + 方向箭头
- **目的地**：绿色方块
- **路径**：蓝色直线 (简化版)
- **网格**：灰色虚线网格

#### 导航计算
- 距离计算：使用球面距离公式
- 方位角计算：使用球面三角学
- 实时更新：500ms刷新周期

### 📊 显示布局

```
┌─────────────────────────────────────┐
│ WANDA Navigation Map                │  ← 标题 (y=10)
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │        地图显示区域              │ │  ← 地图区域
│ │      (220x200像素)              │ │    (x=10, y=50)
│ │                                 │ │
│ │   ●当前位置    ■目的地          │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Distance: 1234m                     │  ← 距离信息 (y=260)
│ Bearing: 045deg                     │  ← 方位信息 (y=280)
└─────────────────────────────────────┘
```

### 🎨 颜色方案
- **背景色**：黑色 (BLACK)
- **文字色**：白色 (WHITE)
- **网格色**：灰色 (GRAY)
- **当前位置**：红色 (RED)
- **目的地**：绿色 (GREEN)
- **路径线**：蓝色 (BLUE)
- **距离信息**：黄色 (YELLOW)
- **方位信息**：青色 (CYAN)

### 🔄 扩展功能

未来可以添加的功能：
1. **多点路径**：支持途经点导航
2. **地图缩放**：支持手动缩放控制
3. **路况信息**：显示交通状况
4. **语音提示**：结合语音导航
5. **历史轨迹**：显示行驶轨迹

### 🐛 故障排除

#### 地图不显示
- 检查LCD是否正常工作
- 确认GPS坐标是否有效
- 检查地图初始化是否成功

#### 位置不准确
- 检查GPS数据格式
- 确认坐标转换算法
- 验证地图中心点设置

#### 显示异常
- 检查颜色定义
- 确认屏幕分辨率设置
- 验证绘图函数调用

现在你的WANDA检测器已经具备了完整的LCD地图显示功能！🎉
